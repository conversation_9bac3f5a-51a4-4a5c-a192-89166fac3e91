import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_button.dart';
import '../../../../core/models/user_model.dart';
import '../../../auth/data/providers/auth_provider.dart';

/// Profile edit screen
class ProfileEditScreen extends ConsumerStatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  ConsumerState<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends ConsumerState<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  late final TextEditingController _fullNameController;
  late final TextEditingController _phoneController;
  late final TextEditingController _farmNameController;
  late final TextEditingController _farmLocationController;
  late final TextEditingController _farmSizeController;
  late final TextEditingController _bioController;
  late final TextEditingController _experienceController;

  String _selectedUserType = 'farmer';
  List<String> _selectedSpecializations = [];
  bool _notificationsEnabled = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    final user = ref.read(currentUserProvider);

    _fullNameController = TextEditingController(text: user?.fullName ?? '');
    _phoneController = TextEditingController(text: user?.phoneNumber ?? '');
    _farmNameController = TextEditingController(text: user?.farmName ?? '');
    _farmLocationController = TextEditingController(
      text: user?.farmLocation ?? '',
    );
    _farmSizeController = TextEditingController(
      text: user?.farmSizeAcres?.toString() ?? '',
    );
    _bioController = TextEditingController(text: user?.bio ?? '');
    _experienceController = TextEditingController(
      text: user?.yearsExperience?.toString() ?? '',
    );

    _selectedUserType = user?.userType ?? 'farmer';
    _selectedSpecializations = user?.specializations ?? [];
    _notificationsEnabled = user?.notificationsEnabled ?? true;
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _farmNameController.dispose();
    _farmLocationController.dispose();
    _farmSizeController.dispose();
    _bioController.dispose();
    _experienceController.dispose();
    super.dispose();
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final update = UserUpdate(
        fullName: _fullNameController.text.trim().isNotEmpty
            ? _fullNameController.text.trim()
            : null,
        phoneNumber: _phoneController.text.trim().isNotEmpty
            ? _phoneController.text.trim()
            : null,
        farmName: _farmNameController.text.trim().isNotEmpty
            ? _farmNameController.text.trim()
            : null,
        farmLocation: _farmLocationController.text.trim().isNotEmpty
            ? _farmLocationController.text.trim()
            : null,
        farmSizeAcres: _farmSizeController.text.trim().isNotEmpty
            ? double.tryParse(_farmSizeController.text.trim())
            : null,
        bio: _bioController.text.trim().isNotEmpty
            ? _bioController.text.trim()
            : null,
        yearsExperience: _experienceController.text.trim().isNotEmpty
            ? int.tryParse(_experienceController.text.trim())
            : null,
        specializations: _selectedSpecializations.isNotEmpty
            ? _selectedSpecializations
            : null,
        notificationsEnabled: _notificationsEnabled,
      );

      final authNotifier = ref.read(authProvider.notifier);
      final success = await authNotifier.updateProfile(update);

      if (mounted) {
        if (success) {
          _showSuccessSnackBar('Profile updated successfully');
          Navigator.of(context).pop();
        } else {
          final error = ref.read(authErrorProvider);
          _showErrorSnackBar(error ?? 'Update failed');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorRed,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showImagePickerDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Change Profile Picture', style: AppTheme.titleMedium),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(MdiIcons.camera, color: AppTheme.primaryGreen),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement camera functionality
                _showSuccessSnackBar('Camera feature coming soon!');
              },
            ),
            ListTile(
              leading: Icon(MdiIcons.image, color: AppTheme.primaryGreen),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement gallery functionality
                _showSuccessSnackBar('Gallery feature coming soon!');
              },
            ),
            ListTile(
              leading: Icon(MdiIcons.accountRemove, color: AppTheme.errorRed),
              title: const Text('Remove Photo'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement remove photo functionality
                _showSuccessSnackBar('Photo removed');
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        actions: [
          IconButton(icon: Icon(MdiIcons.check), onPressed: _updateProfile),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Profile Picture Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          CircleAvatar(
                            radius: 50,
                            backgroundColor: AppTheme.primaryGreen,
                            child: Icon(
                              MdiIcons.account,
                              size: 50,
                              color: AppTheme.textOnPrimary,
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: CircleAvatar(
                              radius: 18,
                              backgroundColor: AppTheme.primaryGreen,
                              child: IconButton(
                                icon: Icon(
                                  MdiIcons.camera,
                                  size: 18,
                                  color: AppTheme.textOnPrimary,
                                ),
                                onPressed: () {
                                  _showImagePickerDialog();
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        user?.email ?? '<EMAIL>',
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          (user?.userType ?? 'farmer').toUpperCase(),
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.primaryGreen,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Personal Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Personal Information', style: AppTheme.titleMedium),
                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _fullNameController,
                        label: 'Full Name',
                        prefixIcon: MdiIcons.account,
                        textCapitalization: TextCapitalization.words,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter your full name';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _phoneController,
                        label: 'Phone Number',
                        prefixIcon: MdiIcons.phone,
                        keyboardType: TextInputType.phone,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Farm Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Farm Information', style: AppTheme.titleMedium),
                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _farmNameController,
                        label: 'Farm Name',
                        prefixIcon: MdiIcons.barn,
                        textCapitalization: TextCapitalization.words,
                      ),

                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _farmLocationController,
                        label: 'Farm Location',
                        prefixIcon: MdiIcons.mapMarker,
                        textCapitalization: TextCapitalization.words,
                      ),

                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _farmSizeController,
                        label: 'Farm Size (acres)',
                        prefixIcon: MdiIcons.ruler,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.trim().isNotEmpty) {
                            final size = double.tryParse(value.trim());
                            if (size == null || size <= 0) {
                              return 'Please enter a valid farm size';
                            }
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Professional Information Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Professional Information',
                        style: AppTheme.titleMedium.copyWith(
                          color: AppTheme.primaryGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // User Type Dropdown
                      DropdownButtonFormField<String>(
                        value: _selectedUserType,
                        decoration: InputDecoration(
                          labelText: 'User Type',
                          prefixIcon: Icon(MdiIcons.accountGroup),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'farmer',
                            child: Text('Farmer'),
                          ),
                          DropdownMenuItem(
                            value: 'expert',
                            child: Text('Expert'),
                          ),
                          DropdownMenuItem(
                            value: 'advisor',
                            child: Text('Advisor'),
                          ),
                          DropdownMenuItem(
                            value: 'student',
                            child: Text('Student'),
                          ),
                          DropdownMenuItem(
                            value: 'researcher',
                            child: Text('Researcher'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedUserType = value ?? 'farmer';
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _experienceController,
                        label: 'Years of Experience',
                        prefixIcon: MdiIcons.calendar,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.trim().isNotEmpty) {
                            final experience = int.tryParse(value.trim());
                            if (experience == null || experience < 0) {
                              return 'Please enter valid years of experience';
                            }
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      CustomTextField(
                        controller: _bioController,
                        label: 'Bio / Description',
                        prefixIcon: MdiIcons.textBox,
                        maxLines: 3,
                        textCapitalization: TextCapitalization.sentences,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Preferences Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Preferences',
                        style: AppTheme.titleMedium.copyWith(
                          color: AppTheme.primaryGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Notifications Toggle
                      SwitchListTile(
                        title: const Text('Enable Notifications'),
                        subtitle: const Text(
                          'Receive alerts and recommendations',
                        ),
                        value: _notificationsEnabled,
                        onChanged: (value) {
                          setState(() {
                            _notificationsEnabled = value;
                          });
                        },
                        activeColor: AppTheme.primaryGreen,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: LoadingButton(
                  onPressed: _updateProfile,
                  isLoading: _isLoading,
                  child: const Text('Save Changes'),
                ),
              ),

              const SizedBox(height: 16),

              // Cancel Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
