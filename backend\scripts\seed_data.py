"""
Database seeding script with sample data
"""
import sys
import os
import json

# Add the parent directory to the path so we can import our app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.config import settings
from app.models.crop import Crop, CropVariety
from app.models.user import User
from app.core.security import get_password_hash
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_session():
    """Create database session"""
    engine = create_engine(settings.database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def seed_crops(db):
    """Seed crop data"""
    logger.info("Seeding crop data...")
    
    crops_data = [
        {
            "name": "Rice",
            "scientific_name": "Oryza sativa",
            "common_names": json.dumps(["<PERSON>", "<PERSON><PERSON>", "Dhan"]),
            "crop_family": "Cereals",
            "crop_type": "Annual",
            "growing_season": "Kharif",
            "maturity_days_min": 90,
            "maturity_days_max": 150,
            "preferred_soil_type": json.dumps(["Clay", "Clay loam"]),
            "ph_range_min": 5.5,
            "ph_range_max": 7.0,
            "temperature_min": 20,
            "temperature_max": 35,
            "rainfall_min": 1000,
            "rainfall_max": 2000,
            "nitrogen_requirement": 120,
            "phosphorus_requirement": 60,
            "potassium_requirement": 40,
            "description": "Rice is a staple cereal grain and one of the most important food crops worldwide.",
            "cultivation_practices": json.dumps({
                "land_preparation": "Puddling for transplanted rice",
                "sowing": "Transplanting or direct seeding",
                "irrigation": "Continuous flooding or alternate wetting and drying",
                "harvesting": "When 80-85% of grains are mature"
            }),
            "common_pests": json.dumps(["Brown planthopper", "Stem borer", "Leaf folder"]),
            "common_diseases": json.dumps(["Blast", "Bacterial blight", "Sheath blight"])
        },
        {
            "name": "Wheat",
            "scientific_name": "Triticum aestivum",
            "common_names": json.dumps(["Gehun", "Gom"]),
            "crop_family": "Cereals",
            "crop_type": "Annual",
            "growing_season": "Rabi",
            "maturity_days_min": 110,
            "maturity_days_max": 130,
            "preferred_soil_type": json.dumps(["Loam", "Clay loam"]),
            "ph_range_min": 6.0,
            "ph_range_max": 7.5,
            "temperature_min": 15,
            "temperature_max": 25,
            "rainfall_min": 300,
            "rainfall_max": 1000,
            "nitrogen_requirement": 120,
            "phosphorus_requirement": 60,
            "potassium_requirement": 40,
            "description": "Wheat is a major cereal grain and staple food crop.",
            "cultivation_practices": json.dumps({
                "land_preparation": "Deep plowing and leveling",
                "sowing": "Line sowing or broadcasting",
                "irrigation": "4-6 irrigations depending on variety",
                "harvesting": "When grains are hard and moisture content is 20-25%"
            }),
            "common_pests": json.dumps(["Aphids", "Termites", "Army worm"]),
            "common_diseases": json.dumps(["Rust", "Smut", "Bunt"])
        },
        {
            "name": "Maize",
            "scientific_name": "Zea mays",
            "common_names": json.dumps(["Corn", "Makka", "Bhutta"]),
            "crop_family": "Cereals",
            "crop_type": "Annual",
            "growing_season": "Kharif",
            "maturity_days_min": 80,
            "maturity_days_max": 120,
            "preferred_soil_type": json.dumps(["Loam", "Sandy loam"]),
            "ph_range_min": 5.8,
            "ph_range_max": 7.8,
            "temperature_min": 21,
            "temperature_max": 27,
            "rainfall_min": 500,
            "rainfall_max": 1200,
            "nitrogen_requirement": 150,
            "phosphorus_requirement": 60,
            "potassium_requirement": 40,
            "description": "Maize is a versatile cereal crop used for food, feed, and industrial purposes.",
            "cultivation_practices": json.dumps({
                "land_preparation": "Deep plowing and harrowing",
                "sowing": "Line sowing with proper spacing",
                "irrigation": "Light and frequent irrigations",
                "harvesting": "When grains are physiologically mature"
            }),
            "common_pests": json.dumps(["Fall armyworm", "Stem borer", "Shoot fly"]),
            "common_diseases": json.dumps(["Blight", "Rust", "Downy mildew"])
        },
        {
            "name": "Cotton",
            "scientific_name": "Gossypium hirsutum",
            "common_names": json.dumps(["Kapas", "Ruyi"]),
            "crop_family": "Fiber crops",
            "crop_type": "Annual",
            "growing_season": "Kharif",
            "maturity_days_min": 160,
            "maturity_days_max": 200,
            "preferred_soil_type": json.dumps(["Black cotton soil", "Alluvial soil"]),
            "ph_range_min": 6.0,
            "ph_range_max": 8.0,
            "temperature_min": 21,
            "temperature_max": 30,
            "rainfall_min": 500,
            "rainfall_max": 1000,
            "nitrogen_requirement": 120,
            "phosphorus_requirement": 60,
            "potassium_requirement": 60,
            "description": "Cotton is the most important fiber crop grown for textile industry.",
            "cultivation_practices": json.dumps({
                "land_preparation": "Deep summer plowing",
                "sowing": "Line sowing with recommended spacing",
                "irrigation": "Based on critical growth stages",
                "harvesting": "Hand picking when bolls are fully opened"
            }),
            "common_pests": json.dumps(["Bollworm", "Aphids", "Thrips", "Whitefly"]),
            "common_diseases": json.dumps(["Wilt", "Blight", "Leaf curl virus"])
        },
        {
            "name": "Sugarcane",
            "scientific_name": "Saccharum officinarum",
            "common_names": json.dumps(["Ganna", "Sherdi"]),
            "crop_family": "Sugar crops",
            "crop_type": "Perennial",
            "growing_season": "Annual",
            "maturity_days_min": 300,
            "maturity_days_max": 365,
            "preferred_soil_type": json.dumps(["Loam", "Clay loam"]),
            "ph_range_min": 6.0,
            "ph_range_max": 7.5,
            "temperature_min": 20,
            "temperature_max": 30,
            "rainfall_min": 1000,
            "rainfall_max": 1500,
            "nitrogen_requirement": 200,
            "phosphorus_requirement": 80,
            "potassium_requirement": 120,
            "description": "Sugarcane is the primary source of sugar and ethanol production.",
            "cultivation_practices": json.dumps({
                "land_preparation": "Deep plowing and leveling",
                "sowing": "Planting of setts in furrows",
                "irrigation": "Regular irrigation throughout the crop period",
                "harvesting": "When sugar content is maximum (10-12 months)"
            }),
            "common_pests": json.dumps(["Shoot borer", "Root borer", "Scale insects"]),
            "common_diseases": json.dumps(["Red rot", "Smut", "Wilt"])
        }
    ]
    
    for crop_data in crops_data:
        existing_crop = db.query(Crop).filter(Crop.name == crop_data["name"]).first()
        if not existing_crop:
            crop = Crop(**crop_data)
            db.add(crop)
            logger.info(f"Added crop: {crop_data['name']}")
    
    db.commit()
    logger.info("Crop data seeding completed!")


def seed_crop_varieties(db):
    """Seed crop variety data"""
    logger.info("Seeding crop variety data...")
    
    # Get rice crop
    rice = db.query(Crop).filter(Crop.name == "Rice").first()
    if rice:
        varieties_data = [
            {
                "crop_id": rice.id,
                "name": "IR64",
                "variety_code": "IR64",
                "breeder_name": "IRRI",
                "release_year": 1985,
                "maturity_days": 115,
                "yield_potential": 6.0,
                "plant_height": 100,
                "drought_tolerance": "medium",
                "disease_resistance": json.dumps(["Blast", "Bacterial blight"]),
                "grain_quality": "Medium grain",
                "suitable_regions": json.dumps(["All rice growing regions"]),
                "suitable_seasons": json.dumps(["Kharif", "Rabi"]),
                "description": "High yielding semi-dwarf variety suitable for irrigated conditions"
            },
            {
                "crop_id": rice.id,
                "name": "Basmati 370",
                "variety_code": "BAS370",
                "breeder_name": "Traditional",
                "release_year": 1933,
                "maturity_days": 145,
                "yield_potential": 4.5,
                "plant_height": 150,
                "drought_tolerance": "low",
                "disease_resistance": json.dumps(["Moderate resistance to blast"]),
                "grain_quality": "Premium aromatic",
                "suitable_regions": json.dumps(["Punjab", "Haryana", "UP"]),
                "suitable_seasons": json.dumps(["Kharif"]),
                "description": "Traditional aromatic rice variety with excellent grain quality"
            }
        ]
        
        for variety_data in varieties_data:
            existing_variety = db.query(CropVariety).filter(
                CropVariety.name == variety_data["name"],
                CropVariety.crop_id == variety_data["crop_id"]
            ).first()
            if not existing_variety:
                variety = CropVariety(**variety_data)
                db.add(variety)
                logger.info(f"Added variety: {variety_data['name']}")
    
    db.commit()
    logger.info("Crop variety data seeding completed!")


def seed_admin_user(db):
    """Seed admin user"""
    logger.info("Seeding admin user...")
    
    admin_data = {
        "email": "<EMAIL>",
        "username": "admin",
        "hashed_password": get_password_hash("admin123"),
        "full_name": "FarmSmart Administrator",
        "user_type": "admin",
        "is_active": True,
        "is_verified": True
    }
    
    existing_admin = db.query(User).filter(User.username == "admin").first()
    if not existing_admin:
        admin_user = User(**admin_data)
        db.add(admin_user)
        db.commit()
        logger.info("Admin user created successfully!")
    else:
        logger.info("Admin user already exists!")


def seed_sample_farmer(db):
    """Seed sample farmer user"""
    logger.info("Seeding sample farmer...")
    
    farmer_data = {
        "email": "<EMAIL>",
        "username": "farmer1",
        "hashed_password": get_password_hash("farmer123"),
        "full_name": "Sample Farmer",
        "phone_number": "+91-9876543210",
        "farm_name": "Green Valley Farm",
        "farm_location": "Punjab, India",
        "farm_size_acres": 10.5,
        "latitude": 30.7333,
        "longitude": 76.7794,
        "user_type": "farmer",
        "is_active": True,
        "is_verified": True
    }
    
    existing_farmer = db.query(User).filter(User.username == "farmer1").first()
    if not existing_farmer:
        farmer_user = User(**farmer_data)
        db.add(farmer_user)
        db.commit()
        logger.info("Sample farmer created successfully!")
    else:
        logger.info("Sample farmer already exists!")


def seed_all_data():
    """Seed all sample data"""
    logger.info("Starting database seeding...")
    
    db = create_session()
    try:
        seed_crops(db)
        seed_crop_varieties(db)
        seed_admin_user(db)
        seed_sample_farmer(db)
        
        logger.info("Database seeding completed successfully!")
        
    except Exception as e:
        logger.error(f"Error seeding database: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    seed_all_data()
