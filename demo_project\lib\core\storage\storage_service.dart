import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';

/// Local storage service for persisting data
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  late SharedPreferences _prefs;
  late Box _settingsBox;
  late Box _cacheBox;

  /// Initialize storage service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _settingsBox = Hive.box('settings');
    _cacheBox = Hive.box('cache');
  }

  // Authentication Token Management

  /// Save authentication token
  Future<void> saveToken(String token) async {
    await _prefs.setString(AppConstants.tokenKey, token);
  }

  /// Get authentication token
  Future<String?> getToken() async {
    return _prefs.getString(AppConstants.tokenKey);
  }

  /// Clear authentication token
  Future<void> clearToken() async {
    await _prefs.remove(AppConstants.tokenKey);
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // User Data Management

  /// Save user data
  Future<void> saveUser(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs.setString(AppConstants.userKey, userJson);
  }

  /// Get user data
  Future<User?> getUser() async {
    final userJson = _prefs.getString(AppConstants.userKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        // If parsing fails, clear corrupted data
        await clearUser();
        return null;
      }
    }
    return null;
  }

  /// Clear user data
  Future<void> clearUser() async {
    await _prefs.remove(AppConstants.userKey);
  }

  /// Clear all authentication data
  Future<void> clearAuthData() async {
    await clearToken();
    await clearUser();
  }

  // App Settings Management

  /// Save app settings
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    await _settingsBox.putAll(settings);
  }

  /// Get app setting
  T? getSetting<T>(String key, {T? defaultValue}) {
    return _settingsBox.get(key, defaultValue: defaultValue) as T?;
  }

  /// Save individual setting
  Future<void> saveSetting(String key, dynamic value) async {
    await _settingsBox.put(key, value);
  }

  /// Get all settings
  Map<String, dynamic> getAllSettings() {
    return Map<String, dynamic>.from(_settingsBox.toMap());
  }

  /// Clear all settings
  Future<void> clearSettings() async {
    await _settingsBox.clear();
  }

  // Cache Management

  /// Save data to cache with expiration
  Future<void> saveToCache(
    String key,
    dynamic data, {
    Duration? expiration,
  }) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiration': expiration?.inMilliseconds,
    };
    await _cacheBox.put(key, cacheData);
  }

  /// Get data from cache
  T? getFromCache<T>(String key) {
    final cacheData = _cacheBox.get(key);
    if (cacheData == null) return null;

    final timestamp = cacheData['timestamp'] as int?;
    final expiration = cacheData['expiration'] as int?;

    if (timestamp != null && expiration != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > expiration) {
        // Cache expired, remove it
        _cacheBox.delete(key);
        return null;
      }
    }

    return cacheData['data'] as T?;
  }

  /// Check if cache has valid data
  bool hasCachedData(String key) {
    return getFromCache(key) != null;
  }

  /// Clear specific cache entry
  Future<void> clearCacheEntry(String key) async {
    await _cacheBox.delete(key);
  }

  /// Clear all cache
  Future<void> clearCache() async {
    await _cacheBox.clear();
  }

  /// Clear expired cache entries
  Future<void> clearExpiredCache() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final keysToDelete = <String>[];

    for (final key in _cacheBox.keys) {
      final cacheData = _cacheBox.get(key);
      if (cacheData is Map) {
        final timestamp = cacheData['timestamp'] as int?;
        final expiration = cacheData['expiration'] as int?;

        if (timestamp != null && expiration != null) {
          if (now - timestamp > expiration) {
            keysToDelete.add(key.toString());
          }
        }
      }
    }

    for (final key in keysToDelete) {
      await _cacheBox.delete(key);
    }
  }

  // Theme and UI Preferences

  /// Save theme mode
  Future<void> saveThemeMode(String themeMode) async {
    await saveSetting('theme_mode', themeMode);
  }

  /// Get theme mode
  String getThemeMode() {
    return getSetting('theme_mode', defaultValue: 'system') ?? 'system';
  }

  /// Save language preference
  Future<void> saveLanguage(String languageCode) async {
    await saveSetting('language', languageCode);
  }

  /// Get language preference
  String getLanguage() {
    return getSetting('language', defaultValue: 'en') ?? 'en';
  }

  // Onboarding and First Launch

  /// Mark onboarding as completed
  Future<void> setOnboardingCompleted() async {
    await saveSetting('onboarding_completed', true);
  }

  /// Check if onboarding is completed
  bool isOnboardingCompleted() {
    return getSetting('onboarding_completed', defaultValue: false) ?? false;
  }

  /// Mark first launch
  Future<void> setFirstLaunch() async {
    await saveSetting('first_launch', false);
  }

  /// Check if this is first launch
  bool isFirstLaunch() {
    return getSetting('first_launch', defaultValue: true) ?? true;
  }

  // Offline Data Management

  /// Save offline data
  Future<void> saveOfflineData(String key, Map<String, dynamic> data) async {
    await _cacheBox.put('offline_$key', {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Get offline data
  Map<String, dynamic>? getOfflineData(String key) {
    final offlineData = _cacheBox.get('offline_$key');
    return offlineData?['data'] as Map<String, dynamic>?;
  }

  /// Clear offline data
  Future<void> clearOfflineData(String key) async {
    await _cacheBox.delete('offline_$key');
  }

  /// Get all offline data keys
  List<String> getOfflineDataKeys() {
    return _cacheBox.keys
        .where((key) => key.toString().startsWith('offline_'))
        .map((key) => key.toString().replaceFirst('offline_', ''))
        .toList();
  }

  // Data Export/Import

  /// Export all user data
  Map<String, dynamic> exportUserData() {
    return {
      'settings': getAllSettings(),
      'cache_keys': _cacheBox.keys.toList(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Clear all data (for logout or reset)
  Future<void> clearAllData() async {
    await clearAuthData();
    await clearSettings();
    await clearCache();
  }
}

/// Storage service provider
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});
