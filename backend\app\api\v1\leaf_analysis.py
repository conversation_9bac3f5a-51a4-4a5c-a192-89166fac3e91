"""
Leaf analysis endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
import os
import uuid

from app.database import get_db
from app.schemas.leaf_analysis import LeafAnalysisCreate, LeafAnalysisUpdate, LeafAnalysisResponse
from app.services.leaf_analysis_service import LeafAnalysisService
from app.core.deps import get_current_active_user
from app.models.user import User
from app.config import settings

router = APIRouter()


@router.post("/", response_model=LeafAnalysisResponse, status_code=status.HTTP_201_CREATED)
def create_leaf_analysis(
    analysis_data: LeafAnalysisCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new leaf analysis record"""
    leaf_service = LeafAnalysisService(db)
    
    try:
        analysis = leaf_service.create_leaf_analysis(current_user.id, analysis_data)
        return analysis
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating leaf analysis"
        )


@router.get("/", response_model=List[LeafAnalysisResponse])
def get_user_leaf_analyses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all leaf analyses for current user"""
    leaf_service = LeafAnalysisService(db)
    
    analyses = leaf_service.get_user_leaf_analyses(current_user.id, skip, limit)
    return analyses


@router.get("/{analysis_id}", response_model=LeafAnalysisResponse)
def get_leaf_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific leaf analysis"""
    leaf_service = LeafAnalysisService(db)
    
    analysis = leaf_service.get_leaf_analysis(analysis_id, current_user.id)
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf analysis not found"
        )
    
    return analysis


@router.put("/{analysis_id}", response_model=LeafAnalysisResponse)
def update_leaf_analysis(
    analysis_id: int,
    analysis_data: LeafAnalysisUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update leaf analysis"""
    leaf_service = LeafAnalysisService(db)
    
    updated_analysis = leaf_service.update_leaf_analysis(analysis_id, current_user.id, analysis_data)
    if not updated_analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf analysis not found"
        )
    
    return updated_analysis


@router.post("/{analysis_id}/upload-image")
async def upload_leaf_image(
    analysis_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload and process leaf image"""
    leaf_service = LeafAnalysisService(db)
    
    # Verify analysis exists and belongs to user
    analysis = leaf_service.get_leaf_analysis(analysis_id, current_user.id)
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf analysis not found"
        )
    
    # Validate file type
    if not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )
    
    # Check file size
    if file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
        )
    
    try:
        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.upload_dir, "leaf_images", unique_filename)
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Update analysis record with image info
        analysis.image_path = file_path
        analysis.image_filename = unique_filename
        analysis.image_size = len(content)
        db.commit()
        
        # Process image
        processing_result = leaf_service.process_leaf_image(file_path, analysis_id)
        
        return {
            "message": "Image uploaded and processed successfully",
            "filename": unique_filename,
            "file_size": len(content),
            "processing_result": processing_result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing image: {str(e)}"
        )


@router.get("/{analysis_id}/recommendations")
def get_leaf_analysis_recommendations(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get recommendations based on leaf analysis"""
    leaf_service = LeafAnalysisService(db)
    
    analysis = leaf_service.get_leaf_analysis(analysis_id, current_user.id)
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf analysis not found"
        )
    
    # Generate recommendations based on analysis results
    recommendations = []
    
    if analysis.nitrogen_deficiency:
        severity = analysis.deficiency_severity or "moderate"
        dosage_map = {"mild": 40, "moderate": 60, "severe": 100}
        dosage = dosage_map.get(severity, 60)
        
        recommendations.append({
            "type": "nitrogen_fertilizer",
            "priority": "high" if severity == "severe" else "medium",
            "fertilizer": "Urea (46-0-0)",
            "dosage_kg_ha": dosage,
            "application_method": "Top dressing with irrigation",
            "timing": "Within 3-7 days",
            "reason": f"Leaf analysis indicates {severity} nitrogen deficiency"
        })
    
    # Add other deficiency recommendations
    deficiency_map = {
        "phosphorus_deficiency": {"fertilizer": "DAP", "dosage": 50},
        "potassium_deficiency": {"fertilizer": "MOP", "dosage": 40},
        "iron_deficiency": {"fertilizer": "Iron chelate", "dosage": 20},
        "magnesium_deficiency": {"fertilizer": "Magnesium sulfate", "dosage": 25}
    }
    
    for deficiency_attr, rec_data in deficiency_map.items():
        if getattr(analysis, deficiency_attr, False):
            recommendations.append({
                "type": deficiency_attr.replace("_deficiency", "_fertilizer"),
                "priority": "medium",
                "fertilizer": rec_data["fertilizer"],
                "dosage_kg_ha": rec_data["dosage"],
                "application_method": "Foliar spray or soil application",
                "timing": "Within 1-2 weeks",
                "reason": f"Leaf analysis indicates {deficiency_attr.replace('_', ' ')}"
            })
    
    return {
        "analysis_id": analysis_id,
        "crop_type": analysis.crop_type,
        "analysis_date": analysis.analysis_date,
        "recommendations": recommendations,
        "total_recommendations": len(recommendations)
    }


@router.delete("/{analysis_id}")
def delete_leaf_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Archive leaf analysis (soft delete)"""
    leaf_service = LeafAnalysisService(db)
    
    analysis = leaf_service.get_leaf_analysis(analysis_id, current_user.id)
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf analysis not found"
        )
    
    # Update status to archived instead of deleting
    from app.schemas.leaf_analysis import LeafAnalysisUpdate
    update_data = LeafAnalysisUpdate(status="archived")
    leaf_service.update_leaf_analysis(analysis_id, current_user.id, update_data)
    
    return {"message": "Leaf analysis archived successfully"}
