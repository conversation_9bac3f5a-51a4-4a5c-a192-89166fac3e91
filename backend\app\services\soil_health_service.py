"""
Soil health analysis service for processing Soil Health Card data
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime
import json

from app.models.soil_health import SoilHealthCard
from app.models.recommendation import Recommendation, RecommendationType
from app.schemas.soil_health import SoilHealthCardCreate, SoilHealthCardUpdate


class SoilHealthService:
    """Service class for soil health operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_soil_health_card(self, user_id: int, card_data: SoilHealthCardCreate) -> SoilHealthCard:
        """Create a new soil health card"""
        db_card = SoilHealthCard(
            user_id=user_id,
            **card_data.dict()
        )
        
        self.db.add(db_card)
        self.db.commit()
        self.db.refresh(db_card)
        return db_card
    
    def get_soil_health_card(self, card_id: int, user_id: int) -> Optional[SoilHealthCard]:
        """Get soil health card by ID for a specific user"""
        return self.db.query(SoilHealthCard).filter(
            SoilHealthCard.id == card_id,
            SoilHealthCard.user_id == user_id
        ).first()
    
    def get_user_soil_health_cards(self, user_id: int, skip: int = 0, limit: int = 100) -> List[SoilHealthCard]:
        """Get all soil health cards for a user"""
        return self.db.query(SoilHealthCard).filter(
            SoilHealthCard.user_id == user_id,
            SoilHealthCard.status == "active"
        ).offset(skip).limit(limit).all()
    
    def update_soil_health_card(self, card_id: int, user_id: int, card_data: SoilHealthCardUpdate) -> Optional[SoilHealthCard]:
        """Update soil health card"""
        card = self.get_soil_health_card(card_id, user_id)
        if not card:
            return None
        
        update_data = card_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(card, field, value)
        
        card.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(card)
        return card
    
    def analyze_soil_health(self, card: SoilHealthCard) -> Dict[str, Any]:
        """Analyze soil health and generate recommendations"""
        analysis = {
            "overall_health": "good",
            "deficiencies": [],
            "recommendations": [],
            "ph_status": self._analyze_ph(card.ph_level),
            "nutrient_status": self._analyze_nutrients(card),
            "organic_matter_status": self._analyze_organic_carbon(card.organic_carbon),
            "micronutrient_status": self._analyze_micronutrients(card)
        }
        
        # Determine overall health
        deficiency_count = len(analysis["deficiencies"])
        if deficiency_count == 0:
            analysis["overall_health"] = "excellent"
        elif deficiency_count <= 2:
            analysis["overall_health"] = "good"
        elif deficiency_count <= 4:
            analysis["overall_health"] = "fair"
        else:
            analysis["overall_health"] = "poor"
        
        return analysis
    
    def _analyze_ph(self, ph_level: Optional[float]) -> Dict[str, Any]:
        """Analyze soil pH level"""
        if ph_level is None:
            return {"status": "unknown", "message": "pH level not provided"}
        
        if ph_level < 5.5:
            return {
                "status": "acidic",
                "message": "Soil is too acidic",
                "recommendation": "Apply lime to increase pH"
            }
        elif ph_level > 8.5:
            return {
                "status": "alkaline",
                "message": "Soil is too alkaline",
                "recommendation": "Apply sulfur or organic matter to decrease pH"
            }
        elif 6.0 <= ph_level <= 7.5:
            return {
                "status": "optimal",
                "message": "pH level is optimal for most crops"
            }
        else:
            return {
                "status": "suboptimal",
                "message": "pH level is slightly outside optimal range",
                "recommendation": "Monitor and adjust if needed"
            }
    
    def _analyze_nutrients(self, card: SoilHealthCard) -> Dict[str, Any]:
        """Analyze macronutrient levels"""
        nutrients = {
            "nitrogen": self._analyze_nitrogen(card.nitrogen_n),
            "phosphorus": self._analyze_phosphorus(card.phosphorus_p),
            "potassium": self._analyze_potassium(card.potassium_k)
        }
        
        return nutrients
    
    def _analyze_nitrogen(self, nitrogen: Optional[float]) -> Dict[str, Any]:
        """Analyze nitrogen levels"""
        if nitrogen is None:
            return {"status": "unknown", "level": "not_tested"}
        
        if nitrogen < 280:
            return {
                "status": "low",
                "level": "deficient",
                "recommendation": "Apply nitrogen fertilizer (urea or ammonium sulfate)",
                "dosage_kg_ha": 120
            }
        elif nitrogen < 560:
            return {
                "status": "medium",
                "level": "adequate",
                "recommendation": "Maintain current nitrogen levels",
                "dosage_kg_ha": 80
            }
        else:
            return {
                "status": "high",
                "level": "sufficient",
                "recommendation": "Reduce nitrogen application",
                "dosage_kg_ha": 40
            }
    
    def _analyze_phosphorus(self, phosphorus: Optional[float]) -> Dict[str, Any]:
        """Analyze phosphorus levels"""
        if phosphorus is None:
            return {"status": "unknown", "level": "not_tested"}
        
        if phosphorus < 22:
            return {
                "status": "low",
                "level": "deficient",
                "recommendation": "Apply phosphorus fertilizer (DAP or SSP)",
                "dosage_kg_ha": 60
            }
        elif phosphorus < 56:
            return {
                "status": "medium",
                "level": "adequate",
                "recommendation": "Maintain current phosphorus levels",
                "dosage_kg_ha": 40
            }
        else:
            return {
                "status": "high",
                "level": "sufficient",
                "recommendation": "Reduce phosphorus application",
                "dosage_kg_ha": 20
            }
    
    def _analyze_potassium(self, potassium: Optional[float]) -> Dict[str, Any]:
        """Analyze potassium levels"""
        if potassium is None:
            return {"status": "unknown", "level": "not_tested"}
        
        if potassium < 108:
            return {
                "status": "low",
                "level": "deficient",
                "recommendation": "Apply potassium fertilizer (MOP or SOP)",
                "dosage_kg_ha": 60
            }
        elif potassium < 280:
            return {
                "status": "medium",
                "level": "adequate",
                "recommendation": "Maintain current potassium levels",
                "dosage_kg_ha": 40
            }
        else:
            return {
                "status": "high",
                "level": "sufficient",
                "recommendation": "Reduce potassium application",
                "dosage_kg_ha": 20
            }
    
    def _analyze_organic_carbon(self, organic_carbon: Optional[float]) -> Dict[str, Any]:
        """Analyze organic carbon levels"""
        if organic_carbon is None:
            return {"status": "unknown", "level": "not_tested"}
        
        if organic_carbon < 0.5:
            return {
                "status": "low",
                "level": "deficient",
                "recommendation": "Add organic matter (compost, farmyard manure)"
            }
        elif organic_carbon < 0.75:
            return {
                "status": "medium",
                "level": "adequate",
                "recommendation": "Maintain organic matter levels"
            }
        else:
            return {
                "status": "high",
                "level": "sufficient",
                "recommendation": "Excellent organic matter content"
            }
    
    def _analyze_micronutrients(self, card: SoilHealthCard) -> Dict[str, Any]:
        """Analyze micronutrient levels"""
        micronutrients = {}
        
        # Zinc analysis
        if card.zinc_zn is not None:
            if card.zinc_zn < 0.6:
                micronutrients["zinc"] = {
                    "status": "deficient",
                    "recommendation": "Apply zinc sulfate"
                }
            else:
                micronutrients["zinc"] = {"status": "sufficient"}
        
        # Iron analysis
        if card.iron_fe is not None:
            if card.iron_fe < 4.5:
                micronutrients["iron"] = {
                    "status": "deficient",
                    "recommendation": "Apply iron chelate"
                }
            else:
                micronutrients["iron"] = {"status": "sufficient"}
        
        # Boron analysis
        if card.boron_b is not None:
            if card.boron_b < 0.5:
                micronutrients["boron"] = {
                    "status": "deficient",
                    "recommendation": "Apply borax"
                }
            else:
                micronutrients["boron"] = {"status": "sufficient"}
        
        return micronutrients
    
    def generate_fertilizer_recommendations(self, card: SoilHealthCard, crop_type: str = "general") -> List[Dict[str, Any]]:
        """Generate specific fertilizer recommendations based on soil analysis"""
        analysis = self.analyze_soil_health(card)
        recommendations = []
        
        # Nitrogen recommendation
        n_analysis = analysis["nutrient_status"]["nitrogen"]
        if n_analysis["status"] in ["low", "medium"]:
            recommendations.append({
                "type": "nitrogen",
                "fertilizer": "Urea",
                "dosage_kg_ha": n_analysis.get("dosage_kg_ha", 80),
                "application_method": "Split application - 50% at sowing, 50% at tillering",
                "timing": "Apply during active growth periods"
            })
        
        # Phosphorus recommendation
        p_analysis = analysis["nutrient_status"]["phosphorus"]
        if p_analysis["status"] in ["low", "medium"]:
            recommendations.append({
                "type": "phosphorus",
                "fertilizer": "DAP (Diammonium Phosphate)",
                "dosage_kg_ha": p_analysis.get("dosage_kg_ha", 40),
                "application_method": "Basal application at sowing",
                "timing": "Apply before sowing or transplanting"
            })
        
        # Potassium recommendation
        k_analysis = analysis["nutrient_status"]["potassium"]
        if k_analysis["status"] in ["low", "medium"]:
            recommendations.append({
                "type": "potassium",
                "fertilizer": "MOP (Muriate of Potash)",
                "dosage_kg_ha": k_analysis.get("dosage_kg_ha", 40),
                "application_method": "Split application - 50% basal, 50% at flowering",
                "timing": "Apply at sowing and flowering stages"
            })
        
        # Micronutrient recommendations
        micro_analysis = analysis["micronutrient_status"]
        for nutrient, data in micro_analysis.items():
            if data.get("status") == "deficient":
                recommendations.append({
                    "type": f"micronutrient_{nutrient}",
                    "fertilizer": data.get("recommendation", f"{nutrient.title()} fertilizer"),
                    "dosage_kg_ha": self._get_micronutrient_dosage(nutrient),
                    "application_method": "Foliar spray or soil application",
                    "timing": "Apply during vegetative growth"
                })
        
        return recommendations
    
    def _get_micronutrient_dosage(self, nutrient: str) -> float:
        """Get recommended dosage for micronutrients"""
        dosages = {
            "zinc": 25,
            "iron": 20,
            "boron": 10,
            "manganese": 15,
            "copper": 8
        }
        return dosages.get(nutrient, 10)
