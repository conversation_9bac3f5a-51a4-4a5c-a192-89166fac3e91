"""
Weather service for fetching and analyzing weather data
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import httpx
import json

from app.models.weather import WeatherData
from app.config import settings


class WeatherService:
    """Service class for weather operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.api_key = settings.openweather_api_key
        self.base_url = "https://api.openweathermap.org/data/2.5"
    
    async def fetch_current_weather(self, latitude: float, longitude: float) -> Optional[Dict[str, Any]]:
        """Fetch current weather data from OpenWeatherMap API"""
        if not self.api_key:
            return None
        
        url = f"{self.base_url}/weather"
        params = {
            "lat": latitude,
            "lon": longitude,
            "appid": self.api_key,
            "units": "metric"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching weather data: {e}")
            return None
    
    async def fetch_weather_forecast(self, latitude: float, longitude: float, days: int = 5) -> Optional[Dict[str, Any]]:
        """Fetch weather forecast data"""
        if not self.api_key:
            return None
        
        url = f"{self.base_url}/forecast"
        params = {
            "lat": latitude,
            "lon": longitude,
            "appid": self.api_key,
            "units": "metric",
            "cnt": days * 8  # 8 forecasts per day (3-hour intervals)
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching forecast data: {e}")
            return None
    
    def store_weather_data(self, weather_data: Dict[str, Any], latitude: float, longitude: float, data_type: str = "current") -> WeatherData:
        """Store weather data in database"""
        # Extract weather information
        main = weather_data.get("main", {})
        wind = weather_data.get("wind", {})
        clouds = weather_data.get("clouds", {})
        weather = weather_data.get("weather", [{}])[0]
        rain = weather_data.get("rain", {})
        snow = weather_data.get("snow", {})
        sys = weather_data.get("sys", {})
        
        # Create weather data record
        db_weather = WeatherData(
            latitude=latitude,
            longitude=longitude,
            location_name=weather_data.get("name"),
            weather_date=datetime.utcnow(),
            data_type=data_type,
            
            # Temperature data
            temperature=main.get("temp"),
            feels_like=main.get("feels_like"),
            temp_min=main.get("temp_min"),
            temp_max=main.get("temp_max"),
            
            # Atmospheric conditions
            humidity=main.get("humidity"),
            pressure=main.get("pressure"),
            visibility=weather_data.get("visibility", 0) / 1000 if weather_data.get("visibility") else None,
            
            # Wind data
            wind_speed=wind.get("speed"),
            wind_direction=wind.get("deg"),
            wind_gust=wind.get("gust"),
            
            # Precipitation
            rainfall=rain.get("1h", 0) if rain else 0,
            rainfall_1h=rain.get("1h"),
            rainfall_3h=rain.get("3h"),
            snow=snow.get("1h"),
            
            # Cloud and weather conditions
            cloud_cover=clouds.get("all"),
            weather_main=weather.get("main"),
            weather_description=weather.get("description"),
            weather_icon=weather.get("icon"),
            
            # Sunrise and sunset
            sunrise=datetime.fromtimestamp(sys.get("sunrise")) if sys.get("sunrise") else None,
            sunset=datetime.fromtimestamp(sys.get("sunset")) if sys.get("sunset") else None,
            
            # Data source
            data_source="openweather",
            data_quality="good"
        )
        
        self.db.add(db_weather)
        self.db.commit()
        self.db.refresh(db_weather)
        return db_weather
    
    def get_weather_data(self, latitude: float, longitude: float, days_back: int = 7) -> List[WeatherData]:
        """Get stored weather data for a location"""
        start_date = datetime.utcnow() - timedelta(days=days_back)
        
        return self.db.query(WeatherData).filter(
            WeatherData.latitude.between(latitude - 0.1, latitude + 0.1),
            WeatherData.longitude.between(longitude - 0.1, longitude + 0.1),
            WeatherData.weather_date >= start_date
        ).order_by(WeatherData.weather_date.desc()).all()
    
    def analyze_application_timing(self, weather_data: List[WeatherData], application_type: str = "fertilizer") -> Dict[str, Any]:
        """Analyze optimal timing for fertilizer/pesticide application based on weather"""
        if not weather_data:
            return {"status": "no_data", "recommendation": "Weather data not available"}
        
        current_weather = weather_data[0] if weather_data else None
        
        if application_type == "fertilizer":
            return self._analyze_fertilizer_timing(current_weather, weather_data)
        elif application_type == "pesticide":
            return self._analyze_pesticide_timing(current_weather, weather_data)
        else:
            return {"status": "unknown_type", "recommendation": "Unknown application type"}
    
    def _analyze_fertilizer_timing(self, current_weather: WeatherData, weather_history: List[WeatherData]) -> Dict[str, Any]:
        """Analyze optimal timing for fertilizer application"""
        if not current_weather:
            return {"status": "no_data", "recommendation": "Current weather data not available"}
        
        recommendations = []
        timing_score = 100  # Start with perfect score
        
        # Check rainfall
        if current_weather.rainfall and current_weather.rainfall > 5:
            recommendations.append("Avoid application during heavy rainfall")
            timing_score -= 30
        elif current_weather.rainfall and current_weather.rainfall > 2:
            recommendations.append("Light rain detected - consider delaying application")
            timing_score -= 15
        
        # Check wind speed
        if current_weather.wind_speed and current_weather.wind_speed > 15:
            recommendations.append("High wind speed - avoid foliar application")
            timing_score -= 20
        elif current_weather.wind_speed and current_weather.wind_speed > 10:
            recommendations.append("Moderate wind - use caution with foliar application")
            timing_score -= 10
        
        # Check temperature
        if current_weather.temperature and current_weather.temperature > 35:
            recommendations.append("High temperature - apply early morning or evening")
            timing_score -= 15
        elif current_weather.temperature and current_weather.temperature < 10:
            recommendations.append("Low temperature - nutrient uptake may be reduced")
            timing_score -= 10
        
        # Check humidity
        if current_weather.humidity and current_weather.humidity < 40:
            recommendations.append("Low humidity - ensure adequate soil moisture")
            timing_score -= 10
        
        # Predict rainfall in next 24 hours
        recent_weather = [w for w in weather_history if w.weather_date > datetime.utcnow() - timedelta(hours=24)]
        if any(w.rainfall and w.rainfall > 10 for w in recent_weather):
            recommendations.append("Heavy rain expected - delay application")
            timing_score -= 25
        
        # Determine overall recommendation
        if timing_score >= 80:
            status = "optimal"
            overall_recommendation = "Excellent conditions for fertilizer application"
        elif timing_score >= 60:
            status = "good"
            overall_recommendation = "Good conditions for fertilizer application"
        elif timing_score >= 40:
            status = "fair"
            overall_recommendation = "Fair conditions - consider precautions"
        else:
            status = "poor"
            overall_recommendation = "Poor conditions - delay application"
        
        return {
            "status": status,
            "timing_score": timing_score,
            "overall_recommendation": overall_recommendation,
            "specific_recommendations": recommendations,
            "best_application_time": self._get_best_application_time(current_weather)
        }
    
    def _analyze_pesticide_timing(self, current_weather: WeatherData, weather_history: List[WeatherData]) -> Dict[str, Any]:
        """Analyze optimal timing for pesticide application"""
        if not current_weather:
            return {"status": "no_data", "recommendation": "Current weather data not available"}
        
        recommendations = []
        timing_score = 100
        
        # Check rainfall (more critical for pesticides)
        if current_weather.rainfall and current_weather.rainfall > 2:
            recommendations.append("Rainfall will wash away pesticide - delay application")
            timing_score -= 40
        
        # Check wind speed (very critical for pesticides)
        if current_weather.wind_speed and current_weather.wind_speed > 10:
            recommendations.append("High wind speed - risk of drift, avoid application")
            timing_score -= 35
        elif current_weather.wind_speed and current_weather.wind_speed > 5:
            recommendations.append("Moderate wind - use drift-reducing nozzles")
            timing_score -= 15
        
        # Check temperature
        if current_weather.temperature and current_weather.temperature > 30:
            recommendations.append("High temperature - apply early morning or evening")
            timing_score -= 20
        elif current_weather.temperature and current_weather.temperature < 15:
            recommendations.append("Low temperature - reduced pest activity and efficacy")
            timing_score -= 15
        
        # Check humidity (important for fungicides)
        if current_weather.humidity and current_weather.humidity > 80:
            recommendations.append("High humidity - good for fungicide application")
            timing_score += 10
        elif current_weather.humidity and current_weather.humidity < 50:
            recommendations.append("Low humidity - may reduce efficacy")
            timing_score -= 10
        
        # Check for rain forecast
        recent_weather = [w for w in weather_history if w.weather_date > datetime.utcnow() - timedelta(hours=6)]
        if any(w.rainfall and w.rainfall > 5 for w in recent_weather):
            recommendations.append("Rain expected within 6 hours - delay application")
            timing_score -= 30
        
        # Determine overall recommendation
        if timing_score >= 80:
            status = "optimal"
            overall_recommendation = "Excellent conditions for pesticide application"
        elif timing_score >= 60:
            status = "good"
            overall_recommendation = "Good conditions for pesticide application"
        elif timing_score >= 40:
            status = "fair"
            overall_recommendation = "Fair conditions - take precautions"
        else:
            status = "poor"
            overall_recommendation = "Poor conditions - delay application"
        
        return {
            "status": status,
            "timing_score": timing_score,
            "overall_recommendation": overall_recommendation,
            "specific_recommendations": recommendations,
            "best_application_time": self._get_best_application_time(current_weather)
        }
    
    def _get_best_application_time(self, weather: WeatherData) -> str:
        """Get best time of day for application based on weather"""
        if not weather.temperature:
            return "Early morning (6-8 AM) or evening (5-7 PM)"
        
        if weather.temperature > 30:
            return "Early morning (5-7 AM) or late evening (6-8 PM)"
        elif weather.temperature < 15:
            return "Late morning (9-11 AM) when temperature rises"
        else:
            return "Early morning (6-8 AM) or evening (4-6 PM)"
    
    def get_irrigation_recommendations(self, weather_data: List[WeatherData], crop_type: str = "general") -> Dict[str, Any]:
        """Get irrigation recommendations based on weather data"""
        if not weather_data:
            return {"status": "no_data", "recommendation": "Weather data not available"}
        
        # Calculate total rainfall in last 7 days
        week_rainfall = sum(w.rainfall or 0 for w in weather_data if w.weather_date > datetime.utcnow() - timedelta(days=7))
        
        # Get average temperature and humidity
        recent_weather = [w for w in weather_data if w.weather_date > datetime.utcnow() - timedelta(days=3)]
        avg_temp = sum(w.temperature or 0 for w in recent_weather) / len(recent_weather) if recent_weather else 0
        avg_humidity = sum(w.humidity or 0 for w in recent_weather) / len(recent_weather) if recent_weather else 0
        
        # Calculate evapotranspiration estimate
        et_estimate = self._calculate_et_estimate(avg_temp, avg_humidity, weather_data[0].wind_speed or 0)
        
        recommendations = []
        
        if week_rainfall < 10:
            recommendations.append("Low rainfall in past week - irrigation needed")
        elif week_rainfall > 50:
            recommendations.append("Adequate rainfall - reduce irrigation")
        
        if avg_temp > 35:
            recommendations.append("High temperature - increase irrigation frequency")
        elif avg_temp < 20:
            recommendations.append("Moderate temperature - normal irrigation schedule")
        
        if avg_humidity < 40:
            recommendations.append("Low humidity - increase irrigation amount")
        
        return {
            "week_rainfall": week_rainfall,
            "avg_temperature": avg_temp,
            "avg_humidity": avg_humidity,
            "et_estimate": et_estimate,
            "recommendations": recommendations,
            "irrigation_frequency": self._get_irrigation_frequency(week_rainfall, avg_temp, crop_type)
        }
    
    def _calculate_et_estimate(self, temperature: float, humidity: float, wind_speed: float) -> float:
        """Simple ET estimation using temperature, humidity, and wind speed"""
        # Simplified Penman equation approximation
        if temperature <= 0:
            return 0
        
        # Base ET from temperature
        et_temp = (temperature - 5) * 0.1 if temperature > 5 else 0
        
        # Adjust for humidity
        humidity_factor = (100 - humidity) / 100
        et_humidity = et_temp * (1 + humidity_factor * 0.3)
        
        # Adjust for wind
        wind_factor = min(wind_speed * 0.1, 0.5)
        et_final = et_humidity * (1 + wind_factor)
        
        return max(0, et_final)
    
    def _get_irrigation_frequency(self, rainfall: float, temperature: float, crop_type: str) -> str:
        """Get recommended irrigation frequency"""
        if rainfall > 30:
            return "No irrigation needed"
        elif rainfall > 15:
            if temperature > 30:
                return "Light irrigation every 3-4 days"
            else:
                return "Irrigation every 5-7 days"
        else:
            if temperature > 35:
                return "Daily irrigation needed"
            elif temperature > 25:
                return "Irrigation every 2-3 days"
            else:
                return "Irrigation every 3-4 days"
