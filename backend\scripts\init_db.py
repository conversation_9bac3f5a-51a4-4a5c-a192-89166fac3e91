"""
Database initialization script
"""
import sys
import os

# Add the parent directory to the path so we can import our app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from app.database import Base
from app.config import settings
from app.models import *  # Import all models
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_database():
    """Create database and all tables"""
    try:
        # Create engine
        engine = create_engine(settings.database_url)
        
        # Create all tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        
        logger.info("Database tables created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False


def drop_database():
    """Drop all database tables"""
    try:
        # Create engine
        engine = create_engine(settings.database_url)
        
        # Drop all tables
        logger.info("Dropping database tables...")
        Base.metadata.drop_all(bind=engine)
        
        logger.info("Database tables dropped successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error dropping database: {e}")
        return False


def reset_database():
    """Reset database (drop and recreate)"""
    logger.info("Resetting database...")
    
    if drop_database() and create_database():
        logger.info("Database reset completed successfully!")
        return True
    else:
        logger.error("Database reset failed!")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Database management script")
    parser.add_argument(
        "action",
        choices=["create", "drop", "reset"],
        help="Action to perform on the database"
    )
    
    args = parser.parse_args()
    
    if args.action == "create":
        create_database()
    elif args.action == "drop":
        drop_database()
    elif args.action == "reset":
        reset_database()
