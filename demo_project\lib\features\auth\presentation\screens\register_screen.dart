import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_button.dart';
import '../../../../core/models/user_model.dart';
import '../../data/providers/auth_provider.dart';

/// Registration screen for new user signup
class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();

  // Form controllers
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _farmNameController = TextEditingController();
  final _farmLocationController = TextEditingController();
  final _farmSizeController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _acceptTerms = false;
  int _currentPage = 0;
  String _selectedUserType = 'farmer';

  @override
  void dispose() {
    _emailController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _farmNameController.dispose();
    _farmLocationController.dispose();
    _farmSizeController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;
    if (!_acceptTerms) {
      _showErrorSnackBar('Please accept the terms and conditions');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final registration = UserRegistration(
        email: _emailController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
        fullName: _fullNameController.text.trim(),
        phoneNumber: _phoneController.text.trim().isNotEmpty
            ? _phoneController.text.trim()
            : null,
        farmName: _farmNameController.text.trim().isNotEmpty
            ? _farmNameController.text.trim()
            : null,
        farmLocation: _farmLocationController.text.trim().isNotEmpty
            ? _farmLocationController.text.trim()
            : null,
        farmSizeAcres: _farmSizeController.text.trim().isNotEmpty
            ? double.tryParse(_farmSizeController.text.trim())
            : null,
        userType: _selectedUserType,
      );

      final authNotifier = ref.read(authProvider.notifier);
      final success = await authNotifier.register(registration);

      if (mounted) {
        if (success) {
          _showSuccessDialog();
        } else {
          final error = ref.read(authErrorProvider);
          _showErrorSnackBar(error ?? 'Registration failed');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _nextPage() {
    if (_currentPage == 0 && _validateBasicInfo()) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  bool _validateBasicInfo() {
    if (_emailController.text.isEmpty ||
        _usernameController.text.isEmpty ||
        _passwordController.text.isEmpty ||
        _confirmPasswordController.text.isEmpty ||
        _fullNameController.text.isEmpty) {
      _showErrorSnackBar('Please fill in all required fields');
      return false;
    }

    if (_passwordController.text != _confirmPasswordController.text) {
      _showErrorSnackBar('Passwords do not match');
      return false;
    }

    return true;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorRed,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.checkCircle, color: AppTheme.successGreen),
            const SizedBox(width: 8),
            const Text('Registration Successful'),
          ],
        ),
        content: const Text(
          'Your account has been created successfully. Please check your email for verification instructions.',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go(AppRouter.login);
            },
            child: const Text('Continue to Login'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(
          'Create Account',
          style: TextStyle(
            color: const Color(0xFF2E7D32),
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: const Color(0xFF2E7D32),
          ),
          onPressed: () {
            if (_currentPage > 0) {
              _previousPage();
            } else {
              context.go(AppRouter.login);
            }
          },
        ),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Progress Indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Step ${_currentPage + 1} of 2',
                        style: TextStyle(
                          color: const Color(0xFF2E7D32),
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '${((_currentPage + 1) / 2 * 100).round()}% Complete',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: LinearProgressIndicator(
                      value: (_currentPage + 1) / 2,
                      backgroundColor: Colors.grey[200],
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Color(0xFF4CAF50),
                      ),
                      minHeight: 6,
                    ),
                  ),
                ],
              ),
            ),

            // Page Content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (page) {
                  setState(() => _currentPage = page);
                },
                children: [_buildBasicInfoPage(), _buildFarmInfoPage()],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.person_add_rounded,
                    color: const Color(0xFF2E7D32),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Basic Information',
                        style: AppTheme.headlineSmall.copyWith(
                          color: const Color(0xFF2E7D32),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Let\'s start with your basic details',
                        style: AppTheme.bodyMedium.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

          // Email
          CustomTextField(
            controller: _emailController,
            label: 'Email Address *',
            prefixIcon: MdiIcons.email,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Username
          CustomTextField(
            controller: _usernameController,
            label: 'Username *',
            prefixIcon: MdiIcons.account,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a username';
              }
              if (value.length < AppConstants.minUsernameLength) {
                return 'Username must be at least ${AppConstants.minUsernameLength} characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Full Name
          CustomTextField(
            controller: _fullNameController,
            label: 'Full Name *',
            prefixIcon: MdiIcons.accountCircle,
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your full name';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Phone Number
          CustomTextField(
            controller: _phoneController,
            label: 'Phone Number',
            prefixIcon: MdiIcons.phone,
            keyboardType: TextInputType.phone,
          ),

          const SizedBox(height: 16),

          // User Type
          DropdownButtonFormField<String>(
            value: _selectedUserType,
            decoration: const InputDecoration(
              labelText: 'User Type *',
              prefixIcon: Icon(Icons.person_outline),
            ),
            items: AppConstants.userTypes.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.toUpperCase()),
              );
            }).toList(),
            onChanged: (value) {
              setState(() => _selectedUserType = value!);
            },
          ),

          const SizedBox(height: 16),

          // Password
          CustomTextField(
            controller: _passwordController,
            label: 'Password *',
            prefixIcon: MdiIcons.lock,
            suffixIcon: IconButton(
              icon: Icon(_obscurePassword ? MdiIcons.eyeOff : MdiIcons.eye),
              onPressed: () {
                setState(() => _obscurePassword = !_obscurePassword);
              },
            ),
            obscureText: _obscurePassword,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a password';
              }
              if (value.length < AppConstants.minPasswordLength) {
                return 'Password must be at least ${AppConstants.minPasswordLength} characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Confirm Password
          CustomTextField(
            controller: _confirmPasswordController,
            label: 'Confirm Password *',
            prefixIcon: MdiIcons.lockCheck,
            suffixIcon: IconButton(
              icon: Icon(
                _obscureConfirmPassword ? MdiIcons.eyeOff : MdiIcons.eye,
              ),
              onPressed: () {
                setState(
                  () => _obscureConfirmPassword = !_obscureConfirmPassword,
                );
              },
            ),
            obscureText: _obscureConfirmPassword,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),

          const SizedBox(height: 32),

          // Next Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _nextPage,
              child: const Text('Next'),
            ),
          ),

          const SizedBox(height: 16),

          // Login Link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Already have an account? '),
              TextButton(
                onPressed: () => context.go(AppRouter.login),
                child: const Text('Sign In'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFarmInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Farm Information', style: AppTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(
            'Tell us about your farm (optional)',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          ),
          const SizedBox(height: 32),

          // Farm Name
          CustomTextField(
            controller: _farmNameController,
            label: 'Farm Name',
            prefixIcon: MdiIcons.barn,
            textCapitalization: TextCapitalization.words,
          ),

          const SizedBox(height: 16),

          // Farm Location
          CustomTextField(
            controller: _farmLocationController,
            label: 'Farm Location',
            prefixIcon: MdiIcons.mapMarker,
            textCapitalization: TextCapitalization.words,
          ),

          const SizedBox(height: 16),

          // Farm Size
          CustomTextField(
            controller: _farmSizeController,
            label: 'Farm Size (acres)',
            prefixIcon: MdiIcons.ruler,
            keyboardType: TextInputType.number,
          ),

          const SizedBox(height: 32),

          // Terms and Conditions
          CheckboxListTile(
            value: _acceptTerms,
            onChanged: (value) {
              setState(() => _acceptTerms = value ?? false);
            },
            title: const Text('I accept the Terms and Conditions'),
            subtitle: TextButton(
              onPressed: () {
                // TODO: Show terms and conditions
              },
              child: const Text('Read Terms and Conditions'),
            ),
            controlAffinity: ListTileControlAffinity.leading,
          ),

          const SizedBox(height: 24),

          // Register Button
          SizedBox(
            width: double.infinity,
            child: LoadingButton(
              onPressed: _register,
              isLoading: _isLoading,
              child: const Text('Create Account'),
            ),
          ),

          const SizedBox(height: 16),

          // Back Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _previousPage,
              child: const Text('Back'),
            ),
          ),
        ],
      ),
    );
  }
}
