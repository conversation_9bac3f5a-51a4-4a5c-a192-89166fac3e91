import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';

/// Recent activities card for dashboard
class RecentActivitiesCard extends StatelessWidget {
  const RecentActivitiesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.history,
                  color: AppTheme.primaryGreen,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Activities',
                  style: AppTheme.titleMedium,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: Show all activities
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              icon: MdiIcons.testTube,
              title: 'Soil Health Card Added',
              description: 'Field A - pH 6.5, Good organic content',
              time: '2 hours ago',
              color: AppTheme.primaryBrown,
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              icon: MdiIcons.leaf,
              title: 'Leaf Analysis Completed',
              description: 'Field B - Moderate nitrogen deficiency detected',
              time: '1 day ago',
              color: AppTheme.accentGreen,
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              icon: MdiIcons.weatherRainy,
              title: 'Weather Alert',
              description: 'Rain forecast for next 24 hours',
              time: '2 days ago',
              color: AppTheme.infoBlue,
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              icon: MdiIcons.checkCircle,
              title: 'Fertilizer Applied',
              description: 'Field A - 50 kg/ha Urea application completed',
              time: '3 days ago',
              color: AppTheme.successGreen,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String description,
    required String time,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Text(
          time,
          style: AppTheme.labelSmall.copyWith(
            color: AppTheme.textHint,
          ),
        ),
      ],
    );
  }
}
