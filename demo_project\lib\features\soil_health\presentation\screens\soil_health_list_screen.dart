import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/widgets/bottom_navigation.dart';

/// Soil health cards list screen
class SoilHealthListScreen extends ConsumerWidget {
  const SoilHealthListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Soil Health Cards'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.plus),
            onPressed: () => context.go(AppRouter.soilHealthForm),
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 3, // Mock data
        itemBuilder: (context, index) {
          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: AppTheme.primaryBrown.withOpacity(0.1),
                child: Icon(
                  MdiIcons.testTube,
                  color: AppTheme.primaryBrown,
                ),
              ),
              title: Text('Field ${String.fromCharCode(65 + index)}'),
              subtitle: Text('pH: ${6.5 + index * 0.3} • Added ${index + 1} days ago'),
              trailing: Icon(MdiIcons.chevronRight),
              onTap: () => context.go('${AppRouter.soilHealthDetail}/${index + 1}'),
            ),
          );
        },
      ),
      bottomNavigationBar: const AppBottomNavigation(currentIndex: 1),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRouter.soilHealthForm),
        child: Icon(MdiIcons.plus),
      ),
    );
  }
}
