# FarmSmart AI Backend

## Overview

FarmSmart AI is a comprehensive precision agriculture backend system that integrates soil health data, weather information, and leaf color analysis to provide AI-powered farming recommendations. The system helps farmers optimize fertilizer and pesticide usage, improve crop health, and promote sustainable agriculture practices.

## Features

### Core Functionality
- **User Management**: Registration, authentication, and profile management for farmers, advisors, and administrators
- **Soil Health Analysis**: Process Soil Health Card data and generate fertilizer recommendations based on N, P, K, pH levels
- **Weather Integration**: Real-time weather data fetching and analysis for optimal application timing
- **Leaf Color Analysis**: AI-powered image processing for nitrogen deficiency detection using Leaf Color Chart methodology
- **AI Recommendations**: Machine learning models that combine multiple data sources for personalized farming advice

### Key Components
- **RESTful API**: Comprehensive FastAPI-based REST API with automatic documentation
- **Database Models**: SQLAlchemy models for users, soil health, weather, leaf analysis, crops, and recommendations
- **Authentication**: JWT-based authentication with role-based access control
- **Image Processing**: OpenCV and ML-based leaf image analysis
- **Weather Services**: Integration with OpenWeatherMap API
- **Data Validation**: Comprehensive input validation and error handling

## Technology Stack

- **Framework**: FastAPI (Python 3.8+)
- **Database**: SQLAlchemy with SQLite/PostgreSQL support
- **Authentication**: JWT tokens with bcrypt password hashing
- **Image Processing**: OpenCV, PIL, scikit-learn
- **Weather API**: OpenWeatherMap integration
- **Testing**: pytest with comprehensive test coverage
- **Documentation**: Automatic API documentation with Swagger/OpenAPI

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager
- Virtual environment (recommended)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Initialize Database**
   ```bash
   python scripts/init_db.py create
   python scripts/seed_data.py
   ```

6. **Run the application**
   ```bash
   uvicorn app.main:app --reload
   ```

The API will be available at `http://localhost:8000`

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Database Configuration
DATABASE_URL=sqlite:///./farmsmart.db

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Weather API
OPENWEATHER_API_KEY=your-openweather-api-key

# Application Settings
DEBUG=True
HOST=0.0.0.0
PORT=8000

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads/

# ML Model Settings
MODEL_PATH=models/
LEAF_COLOR_MODEL_PATH=models/leaf_color_model.pkl

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## API Documentation

### Interactive Documentation
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Main API Endpoints

#### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/login-json` - JSON-based login

#### User Management
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update user profile
- `GET /api/v1/users/{user_id}` - Get user by ID

#### Soil Health
- `POST /api/v1/soil-health/` - Create soil health card
- `GET /api/v1/soil-health/` - Get user's soil health cards
- `GET /api/v1/soil-health/{card_id}` - Get specific card
- `GET /api/v1/soil-health/{card_id}/analysis` - Get soil analysis
- `GET /api/v1/soil-health/{card_id}/recommendations` - Get fertilizer recommendations

#### Weather
- `GET /api/v1/weather/current` - Get current weather
- `GET /api/v1/weather/forecast` - Get weather forecast
- `GET /api/v1/weather/application-timing` - Get application timing analysis
- `GET /api/v1/weather/irrigation-recommendations` - Get irrigation recommendations

#### Leaf Analysis
- `POST /api/v1/leaf-analysis/` - Create leaf analysis
- `POST /api/v1/leaf-analysis/{id}/upload-image` - Upload leaf image
- `GET /api/v1/leaf-analysis/{id}/recommendations` - Get leaf-based recommendations

#### Recommendations
- `POST /api/v1/recommendations/generate` - Generate comprehensive recommendations
- `GET /api/v1/recommendations/` - Get user recommendations
- `PUT /api/v1/recommendations/{id}` - Update recommendation status

#### Crops
- `GET /api/v1/crops/` - Get crop database
- `GET /api/v1/crops/{crop_id}/varieties` - Get crop varieties
- `POST /api/v1/crops/search` - Advanced crop search
- `GET /api/v1/crops/recommendations/suitable` - Get suitable crops for location

## Database Schema

### Core Models

#### User
- User authentication and profile information
- Farm details and location data
- User type (farmer, advisor, admin)

#### SoilHealthCard
- Soil analysis data (N, P, K, pH, micronutrients)
- Lab information and recommendations
- Location and field details

#### WeatherData
- Current and forecast weather information
- Agricultural parameters (ET, soil temperature)
- Data quality and source tracking

#### LeafAnalysis
- Leaf color chart readings
- AI-based image analysis results
- Deficiency detection and severity assessment

#### Recommendation
- AI-generated farming recommendations
- Fertilizer and pesticide suggestions
- Timing and application details

#### Crop & CropVariety
- Comprehensive crop database
- Variety-specific information
- Growing requirements and characteristics

## Testing

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_auth.py

# Run with verbose output
pytest -v
```

### Test Structure
- `tests/conftest.py` - Test configuration and fixtures
- `tests/test_auth.py` - Authentication tests
- `tests/test_soil_health.py` - Soil health functionality tests
- `tests/test_*.py` - Additional test modules

## Development

### Code Structure
```
backend/
├── app/
│   ├── api/v1/          # API endpoints
│   ├── core/            # Core utilities (auth, config, etc.)
│   ├── models/          # Database models
│   ├── schemas/         # Pydantic schemas
│   ├── services/        # Business logic services
│   ├── config.py        # Configuration settings
│   ├── database.py      # Database setup
│   └── main.py          # FastAPI application
├── migrations/          # Database migrations
├── scripts/            # Utility scripts
├── tests/              # Test suite
├── uploads/            # File uploads directory
├── logs/               # Application logs
└── requirements.txt    # Python dependencies
```

### Adding New Features

1. **Create Database Model** (if needed)
   - Add model in `app/models/`
   - Create migration script

2. **Create Pydantic Schemas**
   - Add schemas in `app/schemas/`

3. **Implement Service Layer**
   - Add business logic in `app/services/`

4. **Create API Endpoints**
   - Add endpoints in `app/api/v1/`

5. **Write Tests**
   - Add tests in `tests/`

6. **Update Documentation**
   - Update README and API docs

### Database Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Downgrade migration
alembic downgrade -1
```

## Deployment

### Production Setup

1. **Environment Configuration**
   - Set production environment variables
   - Configure PostgreSQL database
   - Set up proper logging

2. **Database Setup**
   ```bash
   alembic upgrade head
   python scripts/seed_data.py
   ```

3. **Run with Gunicorn**
   ```bash
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation at `/docs`
