import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';

/// Soil health card detail screen
class SoilHealthDetailScreen extends ConsumerWidget {
  final int cardId;
  
  const SoilHealthDetailScreen({super.key, required this.cardId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Field ${String.fromCharCode(64 + cardId)}'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.pencil),
            onPressed: () {
              // TODO: Edit soil health card
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Soil Analysis Results', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    _buildAnalysisRow('pH Level', '6.5', 'Optimal'),
                    _buildAnalysisRow('Nitrogen (N)', '280 kg/ha', 'Medium'),
                    _buildAnalysisRow('Phosphorus (P)', '25 kg/ha', 'High'),
                    _buildAnalysisRow('Potassium (K)', '150 kg/ha', 'Medium'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Recommendations', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    _buildRecommendationItem(
                      'Apply Nitrogen Fertilizer',
                      'Apply 60 kg/ha of Urea',
                      AppTheme.warningOrange,
                    ),
                    _buildRecommendationItem(
                      'Maintain pH Level',
                      'Current pH is optimal',
                      AppTheme.successGreen,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value, String status) {
    Color statusColor = AppTheme.successGreen;
    if (status == 'Low') statusColor = AppTheme.errorRed;
    if (status == 'Medium') statusColor = AppTheme.warningOrange;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(child: Text(label)),
          Text(value, style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600)),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: AppTheme.labelSmall.copyWith(color: statusColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(String title, String description, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(MdiIcons.lightbulbOn, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600)),
                Text(description, style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
