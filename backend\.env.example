# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/farmsmart_db
# For SQLite (development): DATABASE_URL=sqlite:///./farmsmart.db

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Weather API
OPENWEATHER_API_KEY=your-openweather-api-key-here

# Application Settings
DEBUG=True
HOST=0.0.0.0
PORT=8000

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/

# ML Model Settings
MODEL_PATH=models/
LEAF_COLOR_MODEL_PATH=models/leaf_color_model.pkl

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
