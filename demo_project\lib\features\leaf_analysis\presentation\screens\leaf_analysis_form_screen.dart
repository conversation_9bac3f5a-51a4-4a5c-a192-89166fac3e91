import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_button.dart';

/// Leaf analysis form screen
class LeafAnalysisFormScreen extends ConsumerStatefulWidget {
  final String? analysisId;
  
  const LeafAnalysisFormScreen({super.key, this.analysisId});

  @override
  ConsumerState<LeafAnalysisFormScreen> createState() => _LeafAnalysisFormScreenState();
}

class _LeafAnalysisFormScreenState extends ConsumerState<LeafAnalysisFormScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.analysisId == null ? 'New Leaf Analysis' : 'Edit Leaf Analysis'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Image Capture Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(MdiIcons.camera, size: 64, color: AppTheme.primaryGreen),
                      const SizedBox(height: 16),
                      Text('Capture Leaf Image', style: AppTheme.titleMedium),
                      const SizedBox(height: 8),
                      Text(
                        'Take a clear photo of the leaf for AI analysis',
                        style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () {
                                // TODO: Open camera
                              },
                              icon: Icon(MdiIcons.camera),
                              label: const Text('Camera'),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () {
                                // TODO: Open gallery
                              },
                              icon: Icon(MdiIcons.image),
                              label: const Text('Gallery'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              CustomTextField(
                label: 'Crop Type',
                prefixIcon: MdiIcons.sprout,
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              const SizedBox(height: 16),
              CustomTextField(
                label: 'Field Name',
                prefixIcon: MdiIcons.barn,
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              const SizedBox(height: 16),
              CustomTextField(
                label: 'Growth Stage',
                prefixIcon: MdiIcons.leaf,
              ),
              const SizedBox(height: 16),
              CustomTextField(
                label: 'LCC Reading (1-6)',
                prefixIcon: MdiIcons.palette,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: LoadingButton(
                  onPressed: _saveLeafAnalysis,
                  isLoading: _isLoading,
                  child: const Text('Analyze'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveLeafAnalysis() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    
    // TODO: Implement analysis logic
    await Future.delayed(const Duration(seconds: 3));
    
    if (mounted) {
      Navigator.of(context).pop();
    }
  }
}
