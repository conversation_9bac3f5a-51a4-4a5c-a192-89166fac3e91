import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../router/app_router.dart';
import '../theme/app_theme.dart';
import '../../features/auth/data/providers/auth_provider.dart';

/// Application drawer for navigation and user actions
class AppDrawer extends ConsumerWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Drawer(
      child: Column(
        children: [
          // User Header
          _buildUserHeader(context, ref),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.viewDashboard,
                  title: 'Dashboard',
                  onTap: () => _navigateTo(context, AppRouter.dashboard),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.account,
                  title: 'Profile',
                  onTap: () => _navigateTo(context, AppRouter.profile),
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.testTube,
                  title: 'Soil Health',
                  onTap: () => _navigateTo(context, AppRouter.soilHealth),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.weatherSunny,
                  title: 'Weather',
                  onTap: () => _navigateTo(context, AppRouter.weather),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.leaf,
                  title: 'Leaf Analysis',
                  onTap: () => _navigateTo(context, AppRouter.leafAnalysis),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.lightbulbOn,
                  title: 'Recommendations',
                  onTap: () => _navigateTo(context, AppRouter.recommendations),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.sprout,
                  title: 'Crops Database',
                  onTap: () => _navigateTo(context, AppRouter.crops),
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.cog,
                  title: 'Settings',
                  onTap: () => _navigateTo(context, AppRouter.settings),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.helpCircle,
                  title: 'Help & Support',
                  onTap: () => _showHelpDialog(context),
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.information,
                  title: 'About',
                  onTap: () => _showAboutDialog(context),
                ),
              ],
            ),
          ),

          // Logout Button
          _buildLogoutButton(context, ref),
        ],
      ),
    );
  }

  Widget _buildUserHeader(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider);

    return UserAccountsDrawerHeader(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppTheme.primaryGreen, AppTheme.lightGreen],
        ),
      ),
      currentAccountPicture: CircleAvatar(
        backgroundColor: AppTheme.textOnPrimary,
        child: Icon(MdiIcons.account, size: 40, color: AppTheme.primaryGreen),
      ),
      accountName: Text(
        user?.fullName ?? 'User Name',
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
      ),
      accountEmail: Text(
        user?.email ?? '<EMAIL>',
        style: const TextStyle(fontSize: 14),
      ),
      otherAccountsPictures: [
        IconButton(
          icon: Icon(MdiIcons.pencil, color: AppTheme.textOnPrimary),
          onPressed: () => _navigateTo(context, AppRouter.profile),
        ),
      ],
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
    );
  }

  Widget _buildLogoutButton(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: () => _logout(context, ref),
          icon: Icon(MdiIcons.logout),
          label: const Text('Logout'),
          style: OutlinedButton.styleFrom(
            foregroundColor: AppTheme.errorRed,
            side: const BorderSide(color: AppTheme.errorRed),
          ),
        ),
      ),
    );
  }

  void _navigateTo(BuildContext context, String route) {
    Navigator.of(context).pop(); // Close drawer
    context.go(route);
  }

  void _logout(BuildContext context, WidgetRef ref) {
    Navigator.of(context).pop(); // Close drawer

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final authNotifier = ref.read(authProvider.notifier);
              await authNotifier.logout();
              if (context.mounted) {
                context.go(AppRouter.login);
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorRed),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    Navigator.of(context).pop(); // Close drawer

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.helpCircle, color: AppTheme.primaryGreen),
            const SizedBox(width: 8),
            const Text('Help & Support'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Need help with FarmSmart AI?'),
            SizedBox(height: 16),
            Text('• Check our user guide in Settings'),
            Text('• Contact support: <EMAIL>'),
            Text('• Call: +1-800-FARM-AI'),
            Text('• Visit: www.farmsmart.com/help'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    Navigator.of(context).pop(); // Close drawer

    showAboutDialog(
      context: context,
      applicationName: 'FarmSmart AI',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        MdiIcons.sprout,
        size: 48,
        color: AppTheme.primaryGreen,
      ),
      children: [
        const Text(
          'Precision Agriculture App for Soil Health & Crop Optimization',
        ),
        const SizedBox(height: 16),
        const Text(
          'Integrates Soil Health Card, weather data, and Leaf Color Chart '
          'for sustainable farming recommendations.',
        ),
        const SizedBox(height: 16),
        const Text('© 2024 FarmSmart AI. All rights reserved.'),
      ],
    );
  }
}
