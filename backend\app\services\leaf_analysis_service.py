"""
Leaf analysis service for processing leaf color chart and image analysis
"""
from typing import Optional, List, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
from datetime import datetime
import cv2
import numpy as np
from PIL import Image
import json
import os
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier
import pickle

from app.models.leaf_analysis import LeafAnalysis
from app.schemas.leaf_analysis import LeafAnalysisCreate, LeafAnalysisUpdate
from app.config import settings


class LeafAnalysisService:
    """Service class for leaf analysis operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.model_path = settings.leaf_color_model_path
        self.upload_dir = settings.upload_dir
        
        # Load or create ML model
        self.ml_model = self._load_or_create_model()
        
        # Leaf Color Chart reference values (1-6 scale)
        self.lcc_reference = {
            1: {"rgb": [255, 255, 150], "nitrogen_level": "very_low", "description": "Very pale yellow-green"},
            2: {"rgb": [230, 255, 120], "nitrogen_level": "low", "description": "Pale yellow-green"},
            3: {"rgb": [200, 255, 100], "nitrogen_level": "medium_low", "description": "Light green"},
            4: {"rgb": [150, 255, 80], "nitrogen_level": "medium", "description": "Medium green"},
            5: {"rgb": [100, 255, 60], "nitrogen_level": "medium_high", "description": "Dark green"},
            6: {"rgb": [50, 200, 40], "nitrogen_level": "high", "description": "Very dark green"}
        }
    
    def create_leaf_analysis(self, user_id: int, analysis_data: LeafAnalysisCreate) -> LeafAnalysis:
        """Create a new leaf analysis record"""
        db_analysis = LeafAnalysis(
            user_id=user_id,
            **analysis_data.dict()
        )
        
        self.db.add(db_analysis)
        self.db.commit()
        self.db.refresh(db_analysis)
        return db_analysis
    
    def get_leaf_analysis(self, analysis_id: int, user_id: int) -> Optional[LeafAnalysis]:
        """Get leaf analysis by ID for a specific user"""
        return self.db.query(LeafAnalysis).filter(
            LeafAnalysis.id == analysis_id,
            LeafAnalysis.user_id == user_id
        ).first()
    
    def get_user_leaf_analyses(self, user_id: int, skip: int = 0, limit: int = 100) -> List[LeafAnalysis]:
        """Get all leaf analyses for a user"""
        return self.db.query(LeafAnalysis).filter(
            LeafAnalysis.user_id == user_id,
            LeafAnalysis.status == "active"
        ).offset(skip).limit(limit).all()
    
    def update_leaf_analysis(self, analysis_id: int, user_id: int, analysis_data: LeafAnalysisUpdate) -> Optional[LeafAnalysis]:
        """Update leaf analysis"""
        analysis = self.get_leaf_analysis(analysis_id, user_id)
        if not analysis:
            return None
        
        update_data = analysis_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(analysis, field, value)
        
        analysis.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(analysis)
        return analysis
    
    def process_leaf_image(self, image_path: str, analysis_id: int) -> Dict[str, Any]:
        """Process leaf image and extract color information"""
        try:
            # Load and preprocess image
            image = cv2.imread(image_path)
            if image is None:
                return {"error": "Could not load image"}
            
            # Convert BGR to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Extract leaf region (simplified approach)
            leaf_mask = self._extract_leaf_region(image_rgb)
            leaf_pixels = image_rgb[leaf_mask]
            
            if len(leaf_pixels) == 0:
                return {"error": "No leaf region detected"}
            
            # Analyze colors
            color_analysis = self._analyze_leaf_colors(leaf_pixels)
            
            # Predict nitrogen level using ML model
            nitrogen_prediction = self._predict_nitrogen_level(color_analysis)
            
            # Compare with LCC reference
            lcc_comparison = self._compare_with_lcc(color_analysis)
            
            # Update analysis record
            analysis = self.db.query(LeafAnalysis).filter(LeafAnalysis.id == analysis_id).first()
            if analysis:
                analysis.ai_nitrogen_level = nitrogen_prediction["nitrogen_level"]
                analysis.ai_confidence_score = nitrogen_prediction["confidence"]
                analysis.ai_color_values = json.dumps(color_analysis)
                analysis.lcc_reading = lcc_comparison["closest_lcc"]
                analysis.lcc_category = lcc_comparison["category"]
                
                # Set deficiency flags
                if nitrogen_prediction["nitrogen_level"] < 0.4:
                    analysis.nitrogen_deficiency = True
                    analysis.deficiency_severity = "severe"
                elif nitrogen_prediction["nitrogen_level"] < 0.6:
                    analysis.nitrogen_deficiency = True
                    analysis.deficiency_severity = "moderate"
                elif nitrogen_prediction["nitrogen_level"] < 0.8:
                    analysis.nitrogen_deficiency = True
                    analysis.deficiency_severity = "mild"
                
                self.db.commit()
            
            return {
                "color_analysis": color_analysis,
                "nitrogen_prediction": nitrogen_prediction,
                "lcc_comparison": lcc_comparison,
                "recommendations": self._generate_recommendations(nitrogen_prediction, lcc_comparison)
            }
            
        except Exception as e:
            return {"error": f"Error processing image: {str(e)}"}
    
    def _extract_leaf_region(self, image: np.ndarray) -> np.ndarray:
        """Extract leaf region from image using color segmentation"""
        # Convert to HSV for better color segmentation
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        
        # Define range for green colors (leaves)
        lower_green = np.array([35, 40, 40])
        upper_green = np.array([85, 255, 255])
        
        # Create mask for green regions
        mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Apply morphological operations to clean up mask
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # Find largest contour (main leaf)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            mask = np.zeros_like(mask)
            cv2.fillPoly(mask, [largest_contour], 255)
        
        return mask > 0
    
    def _analyze_leaf_colors(self, leaf_pixels: np.ndarray) -> Dict[str, Any]:
        """Analyze color characteristics of leaf pixels"""
        # Calculate basic color statistics
        mean_rgb = np.mean(leaf_pixels, axis=0)
        std_rgb = np.std(leaf_pixels, axis=0)
        
        # Convert to HSV for better color analysis
        hsv_pixels = cv2.cvtColor(leaf_pixels.reshape(-1, 1, 3).astype(np.uint8), cv2.COLOR_RGB2HSV)
        hsv_pixels = hsv_pixels.reshape(-1, 3)
        
        mean_hsv = np.mean(hsv_pixels, axis=0)
        std_hsv = np.std(hsv_pixels, axis=0)
        
        # Perform color clustering to find dominant colors
        kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
        kmeans.fit(leaf_pixels)
        dominant_colors = kmeans.cluster_centers_
        color_percentages = np.bincount(kmeans.labels_) / len(kmeans.labels_)
        
        # Calculate greenness index
        greenness = self._calculate_greenness_index(mean_rgb)
        
        # Calculate yellowness (indicator of nitrogen deficiency)
        yellowness = self._calculate_yellowness_index(mean_rgb)
        
        return {
            "mean_rgb": mean_rgb.tolist(),
            "std_rgb": std_rgb.tolist(),
            "mean_hsv": mean_hsv.tolist(),
            "std_hsv": std_hsv.tolist(),
            "dominant_colors": dominant_colors.tolist(),
            "color_percentages": color_percentages.tolist(),
            "greenness_index": greenness,
            "yellowness_index": yellowness,
            "total_pixels": len(leaf_pixels)
        }
    
    def _calculate_greenness_index(self, rgb: np.ndarray) -> float:
        """Calculate greenness index from RGB values"""
        r, g, b = rgb
        # Normalized greenness index
        total = r + g + b
        if total == 0:
            return 0
        return (2 * g - r - b) / total
    
    def _calculate_yellowness_index(self, rgb: np.ndarray) -> float:
        """Calculate yellowness index (indicator of nitrogen deficiency)"""
        r, g, b = rgb
        # Higher values indicate more yellow (potential nitrogen deficiency)
        if g == 0:
            return 0
        return (r + g) / (2 * g) if g > 0 else 0
    
    def _predict_nitrogen_level(self, color_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Predict nitrogen level using ML model"""
        try:
            # Prepare features for ML model
            features = [
                color_analysis["greenness_index"],
                color_analysis["yellowness_index"],
                color_analysis["mean_hsv"][1],  # Saturation
                color_analysis["mean_hsv"][2],  # Value
                color_analysis["std_rgb"][1],   # Green channel std
            ]
            
            # Use ML model if available, otherwise use rule-based approach
            if self.ml_model:
                prediction = self.ml_model.predict([features])[0]
                confidence = max(self.ml_model.predict_proba([features])[0])
            else:
                prediction, confidence = self._rule_based_nitrogen_prediction(color_analysis)
            
            return {
                "nitrogen_level": float(prediction),
                "confidence": float(confidence),
                "method": "ml_model" if self.ml_model else "rule_based"
            }
            
        except Exception as e:
            # Fallback to rule-based prediction
            prediction, confidence = self._rule_based_nitrogen_prediction(color_analysis)
            return {
                "nitrogen_level": float(prediction),
                "confidence": float(confidence),
                "method": "rule_based_fallback",
                "error": str(e)
            }
    
    def _rule_based_nitrogen_prediction(self, color_analysis: Dict[str, Any]) -> Tuple[float, float]:
        """Rule-based nitrogen level prediction"""
        greenness = color_analysis["greenness_index"]
        yellowness = color_analysis["yellowness_index"]
        
        # Simple rule-based approach
        if greenness > 0.3 and yellowness < 1.2:
            nitrogen_level = 0.8  # High nitrogen
            confidence = 0.7
        elif greenness > 0.1 and yellowness < 1.5:
            nitrogen_level = 0.6  # Medium nitrogen
            confidence = 0.6
        elif greenness > -0.1 and yellowness < 1.8:
            nitrogen_level = 0.4  # Low nitrogen
            confidence = 0.5
        else:
            nitrogen_level = 0.2  # Very low nitrogen
            confidence = 0.4
        
        return nitrogen_level, confidence
    
    def _compare_with_lcc(self, color_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Compare analyzed colors with Leaf Color Chart reference"""
        mean_rgb = np.array(color_analysis["mean_rgb"])
        
        # Calculate distance to each LCC reference
        distances = {}
        for lcc_value, ref_data in self.lcc_reference.items():
            ref_rgb = np.array(ref_data["rgb"])
            distance = np.linalg.norm(mean_rgb - ref_rgb)
            distances[lcc_value] = distance
        
        # Find closest LCC value
        closest_lcc = min(distances, key=distances.get)
        closest_distance = distances[closest_lcc]
        
        # Determine category
        category = self.lcc_reference[closest_lcc]["nitrogen_level"]
        
        return {
            "closest_lcc": closest_lcc,
            "category": category,
            "distance": closest_distance,
            "all_distances": distances,
            "description": self.lcc_reference[closest_lcc]["description"]
        }
    
    def _generate_recommendations(self, nitrogen_prediction: Dict[str, Any], lcc_comparison: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on analysis results"""
        recommendations = []
        nitrogen_level = nitrogen_prediction["nitrogen_level"]
        lcc_value = lcc_comparison["closest_lcc"]
        
        if nitrogen_level < 0.3 or lcc_value <= 2:
            recommendations.append({
                "type": "urgent_fertilization",
                "priority": "high",
                "action": "Apply nitrogen fertilizer immediately",
                "fertilizer": "Urea (46-0-0)",
                "dosage": "100-120 kg/ha",
                "method": "Top dressing followed by irrigation",
                "timing": "Within 2-3 days"
            })
        elif nitrogen_level < 0.5 or lcc_value <= 3:
            recommendations.append({
                "type": "moderate_fertilization",
                "priority": "medium",
                "action": "Apply nitrogen fertilizer",
                "fertilizer": "Urea (46-0-0) or Ammonium Sulfate",
                "dosage": "60-80 kg/ha",
                "method": "Top dressing",
                "timing": "Within 1 week"
            })
        elif nitrogen_level < 0.7 or lcc_value <= 4:
            recommendations.append({
                "type": "light_fertilization",
                "priority": "low",
                "action": "Consider light nitrogen application",
                "fertilizer": "Urea (46-0-0)",
                "dosage": "40-50 kg/ha",
                "method": "Top dressing",
                "timing": "Within 2 weeks"
            })
        else:
            recommendations.append({
                "type": "maintain",
                "priority": "low",
                "action": "Maintain current nitrogen levels",
                "note": "Nitrogen levels appear adequate",
                "timing": "Monitor in 2-3 weeks"
            })
        
        # Add general recommendations
        recommendations.append({
            "type": "monitoring",
            "priority": "low",
            "action": "Continue monitoring leaf color",
            "frequency": "Weekly during active growth",
            "note": "Use Leaf Color Chart for regular assessment"
        })
        
        return recommendations
    
    def _load_or_create_model(self) -> Optional[RandomForestClassifier]:
        """Load existing ML model or create a new one"""
        try:
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    return pickle.load(f)
            else:
                # Create and train a basic model with synthetic data
                return self._create_basic_model()
        except Exception as e:
            print(f"Error loading ML model: {e}")
            return None
    
    def _create_basic_model(self) -> RandomForestClassifier:
        """Create a basic ML model with synthetic training data"""
        # Generate synthetic training data based on known relationships
        np.random.seed(42)
        n_samples = 1000
        
        # Features: greenness_index, yellowness_index, saturation, value, green_std
        X = []
        y = []
        
        for _ in range(n_samples):
            # Generate features for different nitrogen levels
            nitrogen_level = np.random.uniform(0, 1)
            
            # Greenness increases with nitrogen
            greenness = 0.4 * nitrogen_level + np.random.normal(0, 0.1)
            
            # Yellowness decreases with nitrogen
            yellowness = 2.0 - 1.5 * nitrogen_level + np.random.normal(0, 0.2)
            
            # Saturation and value correlate with nitrogen
            saturation = 100 + 50 * nitrogen_level + np.random.normal(0, 10)
            value = 80 + 40 * nitrogen_level + np.random.normal(0, 10)
            green_std = 20 + 10 * nitrogen_level + np.random.normal(0, 5)
            
            X.append([greenness, yellowness, saturation, value, green_std])
            y.append(nitrogen_level)
        
        # Train model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X, y)
        
        # Save model
        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
        with open(self.model_path, 'wb') as f:
            pickle.dump(model, f)
        
        return model
