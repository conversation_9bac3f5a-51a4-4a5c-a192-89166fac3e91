import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../storage/storage_service.dart';

/// Offline data management service
class OfflineService {
  final StorageService _storageService;
  final Connectivity _connectivity;

  OfflineService(this._storageService, this._connectivity);

  // Keys for offline data
  static const String _soilHealthDataKey = 'offline_soil_health_data';
  static const String _weatherDataKey = 'offline_weather_data';
  static const String _recommendationsKey = 'offline_recommendations';
  static const String _leafAnalysisKey = 'offline_leaf_analysis';
  static const String _syncQueueKey = 'sync_queue';
  static const String _lastSyncKey = 'last_sync_timestamp';

  /// Check if device is online
  Future<bool> isOnline() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  }

  /// Save soil health data offline
  Future<void> saveSoilHealthDataOffline(Map<String, dynamic> data) async {
    final existingData = await getSoilHealthDataOffline();
    existingData.add(data);
    await _storageService.saveToCache(
      _soilHealthDataKey,
      jsonEncode(existingData),
    );

    // Add to sync queue if online
    if (await isOnline()) {
      await _addToSyncQueue('soil_health', data);
    }
  }

  /// Get soil health data offline
  Future<List<Map<String, dynamic>>> getSoilHealthDataOffline() async {
    final data = _storageService.getFromCache(_soilHealthDataKey);
    if (data == null) return [];

    try {
      final List<dynamic> decoded = jsonDecode(data);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  /// Save weather data offline
  Future<void> saveWeatherDataOffline(Map<String, dynamic> data) async {
    await _storageService.saveToCache(_weatherDataKey, jsonEncode(data));
  }

  /// Get weather data offline
  Future<Map<String, dynamic>?> getWeatherDataOffline() async {
    final data = _storageService.getFromCache(_weatherDataKey);
    if (data == null) return null;

    try {
      return jsonDecode(data);
    } catch (e) {
      return null;
    }
  }

  /// Save recommendations offline
  Future<void> saveRecommendationsOffline(
    List<Map<String, dynamic>> recommendations,
  ) async {
    await _storageService.saveToCache(
      _recommendationsKey,
      jsonEncode(recommendations),
    );
  }

  /// Get recommendations offline
  Future<List<Map<String, dynamic>>> getRecommendationsOffline() async {
    final data = _storageService.getFromCache(_recommendationsKey);
    if (data == null) return [];

    try {
      final List<dynamic> decoded = jsonDecode(data);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  /// Save leaf analysis data offline
  Future<void> saveLeafAnalysisOffline(Map<String, dynamic> data) async {
    final existingData = await getLeafAnalysisOffline();
    existingData.add(data);
    await _storageService.saveToCache(
      _leafAnalysisKey,
      jsonEncode(existingData),
    );

    // Add to sync queue if online
    if (await isOnline()) {
      await _addToSyncQueue('leaf_analysis', data);
    }
  }

  /// Get leaf analysis data offline
  Future<List<Map<String, dynamic>>> getLeafAnalysisOffline() async {
    final data = _storageService.getFromCache(_leafAnalysisKey);
    if (data == null) return [];

    try {
      final List<dynamic> decoded = jsonDecode(data);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  /// Add item to sync queue
  Future<void> _addToSyncQueue(String type, Map<String, dynamic> data) async {
    final queue = await getSyncQueue();
    queue.add({
      'type': type,
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
    });
    await _storageService.saveToCache(_syncQueueKey, jsonEncode(queue));
  }

  /// Get sync queue
  Future<List<Map<String, dynamic>>> getSyncQueue() async {
    final data = _storageService.getFromCache(_syncQueueKey);
    if (data == null) return [];

    try {
      final List<dynamic> decoded = jsonDecode(data);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  /// Clear sync queue
  Future<void> clearSyncQueue() async {
    await _storageService.clearCacheEntry(_syncQueueKey);
  }

  /// Remove item from sync queue
  Future<void> removeFromSyncQueue(String id) async {
    final queue = await getSyncQueue();
    queue.removeWhere((item) => item['id'] == id);
    await _storageService.saveToCache(_syncQueueKey, jsonEncode(queue));
  }

  /// Sync pending data when online
  Future<bool> syncPendingData() async {
    if (!await isOnline()) return false;

    try {
      final queue = await getSyncQueue();

      for (final item in queue) {
        // Simulate API sync
        await Future.delayed(const Duration(milliseconds: 500));

        // Remove from queue after successful sync
        await removeFromSyncQueue(item['id']);
      }

      // Update last sync timestamp
      await _storageService.saveToCache(
        _lastSyncKey,
        DateTime.now().millisecondsSinceEpoch.toString(),
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime() async {
    final timestamp = _storageService.getFromCache(_lastSyncKey);
    if (timestamp == null) return null;

    try {
      return DateTime.fromMillisecondsSinceEpoch(int.parse(timestamp));
    } catch (e) {
      return null;
    }
  }

  /// Check if data needs sync
  Future<bool> needsSync() async {
    final queue = await getSyncQueue();
    return queue.isNotEmpty;
  }

  /// Get sync status
  Future<Map<String, dynamic>> getSyncStatus() async {
    final isOnlineStatus = await isOnline();
    final needsSyncStatus = await needsSync();
    final lastSync = await getLastSyncTime();
    final queueCount = (await getSyncQueue()).length;

    return {
      'isOnline': isOnlineStatus,
      'needsSync': needsSyncStatus,
      'lastSync': lastSync,
      'pendingItems': queueCount,
    };
  }

  /// Clear all offline data
  Future<void> clearAllOfflineData() async {
    await _storageService.clearCacheEntry(_soilHealthDataKey);
    await _storageService.clearCacheEntry(_weatherDataKey);
    await _storageService.clearCacheEntry(_recommendationsKey);
    await _storageService.clearCacheEntry(_leafAnalysisKey);
    await clearSyncQueue();
    await _storageService.clearCacheEntry(_lastSyncKey);
  }

  /// Export offline data
  Future<Map<String, dynamic>> exportOfflineData() async {
    return {
      'soilHealth': await getSoilHealthDataOffline(),
      'weather': await getWeatherDataOffline(),
      'recommendations': await getRecommendationsOffline(),
      'leafAnalysis': await getLeafAnalysisOffline(),
      'syncQueue': await getSyncQueue(),
      'lastSync': await getLastSyncTime(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  /// Import offline data
  Future<void> importOfflineData(Map<String, dynamic> data) async {
    try {
      if (data['soilHealth'] != null) {
        await _storageService.saveToCache(
          _soilHealthDataKey,
          jsonEncode(data['soilHealth']),
        );
      }

      if (data['weather'] != null) {
        await _storageService.saveToCache(
          _weatherDataKey,
          jsonEncode(data['weather']),
        );
      }

      if (data['recommendations'] != null) {
        await _storageService.saveToCache(
          _recommendationsKey,
          jsonEncode(data['recommendations']),
        );
      }

      if (data['leafAnalysis'] != null) {
        await _storageService.saveToCache(
          _leafAnalysisKey,
          jsonEncode(data['leafAnalysis']),
        );
      }

      if (data['syncQueue'] != null) {
        await _storageService.saveToCache(
          _syncQueueKey,
          jsonEncode(data['syncQueue']),
        );
      }
    } catch (e) {
      throw Exception('Failed to import offline data: $e');
    }
  }
}

/// Offline service provider
final offlineServiceProvider = Provider<OfflineService>((ref) {
  final storageService = StorageService();
  return OfflineService(storageService, Connectivity());
});

/// Connectivity status provider
final connectivityProvider = StreamProvider<List<ConnectivityResult>>((ref) {
  return Connectivity().onConnectivityChanged;
});

/// Sync status provider
final syncStatusProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final offlineService = ref.watch(offlineServiceProvider);
  return await offlineService.getSyncStatus();
});
