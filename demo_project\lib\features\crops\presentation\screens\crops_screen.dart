import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';

/// Crops database screen
class CropsScreen extends ConsumerWidget {
  const CropsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Crops Database'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.magnify),
            onPressed: () {
              // TODO: Search crops
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 8, // Mock data
        itemBuilder: (context, index) {
          final crops = [
            {
              'name': 'Rice',
              'scientific': 'Oryza sativa',
              'family': 'Cereals',
              'season': 'Kharif',
              'icon': MdiIcons.leaf,
              'color': AppTheme.accentGreen,
            },
            {
              'name': 'Wheat',
              'scientific': 'Triticum aestivum',
              'family': 'Cereals',
              'season': 'Rabi',
              'icon': MdiIcons.leaf,
              'color': AppTheme.warningOrange,
            },
            {
              'name': 'Maize',
              'scientific': 'Zea mays',
              'family': 'Cereals',
              'season': 'Kharif',
              'icon': MdiIcons.leaf,
              'color': AppTheme.warningOrange,
            },
            {
              'name': 'Cotton',
              'scientific': 'Gossypium hirsutum',
              'family': 'Fiber crops',
              'season': 'Kharif',
              'icon': MdiIcons.leaf,
              'color': AppTheme.primaryBrown,
            },
            {
              'name': 'Sugarcane',
              'scientific': 'Saccharum officinarum',
              'family': 'Sugar crops',
              'season': 'Annual',
              'icon': MdiIcons.leaf,
              'color': AppTheme.successGreen,
            },
            {
              'name': 'Tomato',
              'scientific': 'Solanum lycopersicum',
              'family': 'Vegetables',
              'season': 'Rabi',
              'icon': MdiIcons.food,
              'color': AppTheme.errorRed,
            },
            {
              'name': 'Potato',
              'scientific': 'Solanum tuberosum',
              'family': 'Vegetables',
              'season': 'Rabi',
              'icon': MdiIcons.food,
              'color': AppTheme.primaryBrown,
            },
            {
              'name': 'Soybean',
              'scientific': 'Glycine max',
              'family': 'Legumes',
              'season': 'Kharif',
              'icon': MdiIcons.leaf,
              'color': AppTheme.accentGreen,
            },
          ];

          final crop = crops[index];

          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: (crop['color'] as Color).withOpacity(0.1),
                child: Icon(
                  crop['icon'] as IconData,
                  color: crop['color'] as Color,
                ),
              ),
              title: Text(crop['name'] as String),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    crop['scientific'] as String,
                    style: AppTheme.bodySmall.copyWith(
                      fontStyle: FontStyle.italic,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          crop['family'] as String,
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.primaryGreen,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.infoBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          crop['season'] as String,
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.infoBlue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              trailing: Icon(MdiIcons.chevronRight),
              isThreeLine: true,
              onTap: () {
                _showCropDetail(context, crop);
              },
            ),
          );
        },
      ),
    );
  }

  void _showCropDetail(BuildContext context, Map<String, dynamic> crop) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    crop['icon'] as IconData,
                    color: crop['color'] as Color,
                    size: 32,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          crop['name'] as String,
                          style: AppTheme.titleLarge,
                        ),
                        Text(
                          crop['scientific'] as String,
                          style: AppTheme.bodyMedium.copyWith(
                            fontStyle: FontStyle.italic,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildDetailSection('Basic Information', [
                      _buildDetailRow('Family', crop['family'] as String),
                      _buildDetailRow(
                        'Growing Season',
                        crop['season'] as String,
                      ),
                      _buildDetailRow('Maturity', '90-150 days'),
                      _buildDetailRow('pH Range', '6.0 - 7.5'),
                    ]),
                    const SizedBox(height: 16),
                    _buildDetailSection('Nutrient Requirements', [
                      _buildDetailRow('Nitrogen (N)', '120 kg/ha'),
                      _buildDetailRow('Phosphorus (P)', '60 kg/ha'),
                      _buildDetailRow('Potassium (K)', '40 kg/ha'),
                    ]),
                    const SizedBox(height: 16),
                    _buildDetailSection('Growing Conditions', [
                      _buildDetailRow('Temperature', '20-35°C'),
                      _buildDetailRow('Rainfall', '1000-2000mm'),
                      _buildDetailRow('Soil Type', 'Clay, Clay loam'),
                    ]),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: AppTheme.titleMedium),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}
