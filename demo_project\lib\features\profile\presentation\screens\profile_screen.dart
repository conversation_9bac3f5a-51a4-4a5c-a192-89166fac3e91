import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/bottom_navigation.dart';
import '../../../auth/data/providers/auth_provider.dart';
import 'profile_edit_screen.dart';

/// User profile screen
class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.pencil),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ProfileEditScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 50,
                      backgroundColor: AppTheme.primaryGreen,
                      child: Icon(
                        MdiIcons.account,
                        size: 50,
                        color: AppTheme.textOnPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      user?.fullName ?? 'User Name',
                      style: AppTheme.headlineSmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user?.email ?? '<EMAIL>',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Chip(
                      label: Text((user?.userType ?? 'farmer').toUpperCase()),
                      backgroundColor: AppTheme.primaryGreen.withOpacity(0.1),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Farm Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Farm Information', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    _buildInfoRow(
                      MdiIcons.barn,
                      'Farm Name',
                      user?.farmName ?? 'Not specified',
                    ),
                    _buildInfoRow(
                      MdiIcons.mapMarker,
                      'Location',
                      user?.farmLocation ?? 'Not specified',
                    ),
                    _buildInfoRow(
                      MdiIcons.ruler,
                      'Farm Size',
                      user?.farmSizeAcres != null
                          ? '${user!.farmSizeAcres} acres'
                          : 'Not specified',
                    ),
                    _buildInfoRow(
                      MdiIcons.phone,
                      'Phone',
                      user?.phoneNumber ?? 'Not specified',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Statistics
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Statistics', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatItem(
                            'Soil Tests',
                            '12',
                            MdiIcons.testTube,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Leaf Analyses',
                            '25',
                            MdiIcons.leaf,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Recommendations',
                            '18',
                            MdiIcons.lightbulbOn,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const AppBottomNavigation(currentIndex: 4),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppTheme.primaryGreen),
          const SizedBox(width: 12),
          Text(
            label,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          ),
          const Spacer(),
          Text(
            value,
            style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryGreen, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.titleLarge.copyWith(
            color: AppTheme.primaryGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
