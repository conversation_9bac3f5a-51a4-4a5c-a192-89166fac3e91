"""
Crops endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.schemas.crop import CropResponse, CropVarietyResponse, CropSearchRequest
from app.core.deps import get_current_active_user
from app.models.user import User
from app.models.crop import Crop, CropVariety

router = APIRouter()


@router.get("/", response_model=List[CropResponse])
def get_crops(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: str = Query(None, description="Search crops by name"),
    crop_family: str = Query(None, description="Filter by crop family"),
    growing_season: str = Query(None, description="Filter by growing season"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of crops with optional filtering"""
    query = db.query(Crop).filter(Crop.is_active == True)
    
    if search:
        query = query.filter(Crop.name.ilike(f"%{search}%"))
    
    if crop_family:
        query = query.filter(Crop.crop_family.ilike(f"%{crop_family}%"))
    
    if growing_season:
        query = query.filter(Crop.growing_season.ilike(f"%{growing_season}%"))
    
    crops = query.offset(skip).limit(limit).all()
    return crops


@router.get("/{crop_id}", response_model=CropResponse)
def get_crop(
    crop_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific crop by ID"""
    crop = db.query(Crop).filter(Crop.id == crop_id, Crop.is_active == True).first()
    if not crop:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Crop not found"
        )
    
    return crop


@router.get("/{crop_id}/varieties", response_model=List[CropVarietyResponse])
def get_crop_varieties(
    crop_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get varieties for a specific crop"""
    # Verify crop exists
    crop = db.query(Crop).filter(Crop.id == crop_id, Crop.is_active == True).first()
    if not crop:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Crop not found"
        )
    
    varieties = db.query(CropVariety).filter(
        CropVariety.crop_id == crop_id,
        CropVariety.is_active == True
    ).offset(skip).limit(limit).all()
    
    return varieties


@router.get("/varieties/{variety_id}", response_model=CropVarietyResponse)
def get_crop_variety(
    variety_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific crop variety by ID"""
    variety = db.query(CropVariety).filter(
        CropVariety.id == variety_id,
        CropVariety.is_active == True
    ).first()
    
    if not variety:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Crop variety not found"
        )
    
    return variety


@router.post("/search", response_model=List[CropResponse])
def search_crops(
    search_request: CropSearchRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Advanced crop search with multiple criteria"""
    query = db.query(Crop).filter(Crop.is_active == True)
    
    if search_request.name:
        query = query.filter(Crop.name.ilike(f"%{search_request.name}%"))
    
    if search_request.crop_family:
        query = query.filter(Crop.crop_family.ilike(f"%{search_request.crop_family}%"))
    
    if search_request.growing_season:
        query = query.filter(Crop.growing_season.ilike(f"%{search_request.growing_season}%"))
    
    if search_request.soil_type:
        query = query.filter(Crop.preferred_soil_type.ilike(f"%{search_request.soil_type}%"))
    
    if search_request.ph_min is not None:
        query = query.filter(Crop.ph_range_min >= search_request.ph_min)
    
    if search_request.ph_max is not None:
        query = query.filter(Crop.ph_range_max <= search_request.ph_max)
    
    if search_request.temperature_min is not None:
        query = query.filter(Crop.temperature_min >= search_request.temperature_min)
    
    if search_request.temperature_max is not None:
        query = query.filter(Crop.temperature_max <= search_request.temperature_max)
    
    if search_request.rainfall_min is not None:
        query = query.filter(Crop.rainfall_min >= search_request.rainfall_min)
    
    if search_request.rainfall_max is not None:
        query = query.filter(Crop.rainfall_max <= search_request.rainfall_max)
    
    crops = query.limit(100).all()
    return crops


@router.get("/recommendations/suitable")
def get_suitable_crops(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    soil_ph: float = Query(None, ge=0, le=14),
    soil_type: str = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get crops suitable for specific location and soil conditions"""
    # This is a simplified implementation
    # In a real system, you would use more sophisticated algorithms
    # considering climate data, soil conditions, etc.
    
    query = db.query(Crop).filter(Crop.is_active == True)
    
    # Filter by soil pH if provided
    if soil_ph is not None:
        query = query.filter(
            Crop.ph_range_min <= soil_ph,
            Crop.ph_range_max >= soil_ph
        )
    
    # Filter by soil type if provided
    if soil_type:
        query = query.filter(Crop.preferred_soil_type.ilike(f"%{soil_type}%"))
    
    suitable_crops = query.limit(20).all()
    
    # Add suitability score (simplified)
    crop_recommendations = []
    for crop in suitable_crops:
        suitability_score = 0.7  # Base score
        
        # Adjust score based on pH match
        if soil_ph and crop.ph_range_min and crop.ph_range_max:
            ph_center = (crop.ph_range_min + crop.ph_range_max) / 2
            ph_diff = abs(soil_ph - ph_center)
            if ph_diff < 0.5:
                suitability_score += 0.2
            elif ph_diff < 1.0:
                suitability_score += 0.1
        
        crop_recommendations.append({
            "crop": crop,
            "suitability_score": min(suitability_score, 1.0),
            "reasons": [
                "Suitable pH range" if soil_ph and crop.ph_range_min <= soil_ph <= crop.ph_range_max else None,
                "Compatible soil type" if soil_type and soil_type.lower() in (crop.preferred_soil_type or "").lower() else None
            ]
        })
    
    # Sort by suitability score
    crop_recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)
    
    return {
        "location": {"latitude": latitude, "longitude": longitude},
        "soil_conditions": {"ph": soil_ph, "type": soil_type},
        "suitable_crops": crop_recommendations[:10],
        "total_found": len(crop_recommendations)
    }


@router.get("/families/list")
def get_crop_families(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of crop families"""
    families = db.query(Crop.crop_family).filter(
        Crop.is_active == True,
        Crop.crop_family.isnot(None)
    ).distinct().all()
    
    family_list = [family[0] for family in families if family[0]]
    family_list.sort()
    
    return {"crop_families": family_list}


@router.get("/seasons/list")
def get_growing_seasons(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of growing seasons"""
    seasons = db.query(Crop.growing_season).filter(
        Crop.is_active == True,
        Crop.growing_season.isnot(None)
    ).distinct().all()
    
    season_list = [season[0] for season in seasons if season[0]]
    season_list.sort()
    
    return {"growing_seasons": season_list}


@router.get("/stats/summary")
def get_crop_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get crop database statistics"""
    total_crops = db.query(Crop).filter(Crop.is_active == True).count()
    total_varieties = db.query(CropVariety).filter(CropVariety.is_active == True).count()
    
    family_counts = {}
    families = db.query(Crop.crop_family).filter(
        Crop.is_active == True,
        Crop.crop_family.isnot(None)
    ).all()
    
    for family in families:
        if family[0]:
            family_counts[family[0]] = family_counts.get(family[0], 0) + 1
    
    return {
        "total_crops": total_crops,
        "total_varieties": total_varieties,
        "crop_families": len(family_counts),
        "family_distribution": family_counts
    }
