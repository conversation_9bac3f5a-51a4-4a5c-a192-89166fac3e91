"""
Custom validators for data validation
"""
import re
from typing import Any, Optional
from fastapi import HTTPException, status


def validate_phone_number(phone: str) -> str:
    """Validate phone number format"""
    if not phone:
        return phone
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (10-15 digits)
    if len(digits_only) < 10 or len(digits_only) > 15:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Phone number must be between 10 and 15 digits"
        )
    
    return phone


def validate_coordinates(latitude: float, longitude: float) -> tuple[float, float]:
    """Validate latitude and longitude coordinates"""
    if not (-90 <= latitude <= 90):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Latitude must be between -90 and 90 degrees"
        )
    
    if not (-180 <= longitude <= 180):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Longitude must be between -180 and 180 degrees"
        )
    
    return latitude, longitude


def validate_ph_level(ph: Optional[float]) -> Optional[float]:
    """Validate pH level"""
    if ph is None:
        return ph
    
    if not (0 <= ph <= 14):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="pH level must be between 0 and 14"
        )
    
    return ph


def validate_percentage(value: Optional[float], field_name: str) -> Optional[float]:
    """Validate percentage values"""
    if value is None:
        return value
    
    if not (0 <= value <= 100):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be between 0 and 100 percent"
        )
    
    return value


def validate_positive_number(value: Optional[float], field_name: str) -> Optional[float]:
    """Validate positive numbers"""
    if value is None:
        return value
    
    if value < 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be a positive number"
        )
    
    return value


def validate_file_size(file_size: int, max_size: int) -> bool:
    """Validate file size"""
    if file_size > max_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size ({file_size} bytes) exceeds maximum allowed size ({max_size} bytes)"
        )
    
    return True


def validate_image_file(content_type: str, filename: str) -> bool:
    """Validate image file type"""
    allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/bmp", "image/tiff"]
    allowed_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif"]
    
    if content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {content_type} not allowed. Allowed types: {', '.join(allowed_types)}"
        )
    
    if filename:
        file_extension = filename.lower().split('.')[-1]
        if f".{file_extension}" not in allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File extension .{file_extension} not allowed. Allowed extensions: {', '.join(allowed_extensions)}"
            )
    
    return True


def validate_lcc_reading(lcc_reading: Optional[float]) -> Optional[float]:
    """Validate Leaf Color Chart reading"""
    if lcc_reading is None:
        return lcc_reading
    
    if not (1 <= lcc_reading <= 6):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Leaf Color Chart reading must be between 1 and 6"
        )
    
    return lcc_reading


def validate_rating(rating: Optional[int]) -> Optional[int]:
    """Validate user rating"""
    if rating is None:
        return rating
    
    if not (1 <= rating <= 5):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rating must be between 1 and 5"
        )
    
    return rating


def validate_crop_name(crop_name: str) -> str:
    """Validate crop name"""
    if not crop_name or len(crop_name.strip()) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Crop name must be at least 2 characters long"
        )
    
    # Check for valid characters (letters, numbers, spaces, hyphens)
    if not re.match(r'^[a-zA-Z0-9\s\-]+$', crop_name):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Crop name can only contain letters, numbers, spaces, and hyphens"
        )
    
    return crop_name.strip()


def validate_fertilizer_dosage(dosage: Optional[float]) -> Optional[float]:
    """Validate fertilizer dosage"""
    if dosage is None:
        return dosage
    
    if dosage < 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Fertilizer dosage cannot be negative"
        )
    
    if dosage > 1000:  # Reasonable upper limit for kg/ha
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Fertilizer dosage seems too high (>1000 kg/ha). Please verify."
        )
    
    return dosage


def validate_farm_size(farm_size: Optional[float]) -> Optional[float]:
    """Validate farm size in acres"""
    if farm_size is None:
        return farm_size
    
    if farm_size <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Farm size must be greater than 0"
        )
    
    if farm_size > 100000:  # Reasonable upper limit
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Farm size seems too large (>100,000 acres). Please verify."
        )
    
    return farm_size


def validate_password_strength(password: str) -> str:
    """Validate password strength"""
    if len(password) < 8:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters long"
        )
    
    if len(password) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be less than 100 characters long"
        )
    
    # Check for at least one uppercase, one lowercase, and one digit
    if not re.search(r'[A-Z]', password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one uppercase letter"
        )
    
    if not re.search(r'[a-z]', password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one lowercase letter"
        )
    
    if not re.search(r'\d', password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must contain at least one digit"
        )
    
    return password


def validate_username(username: str) -> str:
    """Validate username format"""
    if len(username) < 3:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username must be at least 3 characters long"
        )
    
    if len(username) > 50:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username must be less than 50 characters long"
        )
    
    # Check for valid characters (letters, numbers, underscores, hyphens)
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username can only contain letters, numbers, underscores, and hyphens"
        )
    
    return username.lower()
