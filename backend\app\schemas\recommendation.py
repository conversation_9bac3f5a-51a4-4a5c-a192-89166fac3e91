"""
Recommendation schemas for API validation
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.recommendation import RecommendationType


class RecommendationBase(BaseModel):
    """Base recommendation schema"""
    recommendation_type: RecommendationType
    title: str = Field(..., max_length=255)
    description: str
    crop_type: Optional[str] = Field(None, max_length=100)
    crop_variety: Optional[str] = Field(None, max_length=100)
    growth_stage: Optional[str] = Field(None, max_length=50)


class RecommendationCreate(RecommendationBase):
    """Schema for recommendation creation"""
    soil_health_card_id: Optional[int] = None
    leaf_analysis_id: Optional[int] = None
    
    # Fertilizer recommendations
    nitrogen_amount: Optional[float] = Field(None, ge=0)
    phosphorus_amount: Optional[float] = Field(None, ge=0)
    potassium_amount: Optional[float] = Field(None, ge=0)
    fertilizer_type: Optional[str] = Field(None, max_length=100)
    fertilizer_application_method: Optional[str] = Field(None, max_length=100)
    
    # Pesticide recommendations
    pesticide_name: Optional[str] = Field(None, max_length=255)
    pesticide_dosage: Optional[float] = Field(None, ge=0)
    pesticide_unit: Optional[str] = Field(None, max_length=50)
    target_pest_disease: Optional[str] = Field(None, max_length=255)
    
    # Timing
    recommended_date: Optional[datetime] = None
    optimal_time_start: Optional[datetime] = None
    optimal_time_end: Optional[datetime] = None
    
    # Application details
    application_frequency: Optional[str] = Field(None, max_length=100)
    application_interval_days: Optional[int] = Field(None, ge=1)
    total_applications: Optional[int] = Field(None, ge=1)
    
    # Dosage and area
    dosage_per_acre: Optional[float] = Field(None, ge=0)
    dosage_per_hectare: Optional[float] = Field(None, ge=0)
    area_coverage: Optional[float] = Field(None, ge=0)
    
    # Cost estimation
    estimated_cost: Optional[float] = Field(None, ge=0)
    cost_currency: str = Field("INR", max_length=10)
    
    # Expected outcomes
    expected_yield_increase: Optional[float] = Field(None, ge=0, le=100)
    expected_quality_improvement: Optional[str] = None
    environmental_impact: Optional[str] = Field(None, regex="^(low|medium|high)$")
    
    # Priority and urgency
    priority: str = Field("medium", regex="^(low|medium|high|urgent)$")
    urgency_days: Optional[int] = Field(None, ge=1)
    
    # Additional information
    precautions: Optional[str] = None
    alternative_options: Optional[str] = None
    references: Optional[str] = None
    
    # Localization
    language: str = Field("en", max_length=10)
    local_names: Optional[str] = None


class RecommendationUpdate(BaseModel):
    """Schema for recommendation updates"""
    status: Optional[str] = Field(None, regex="^(pending|accepted|rejected|completed)$")
    user_feedback: Optional[str] = None
    user_rating: Optional[int] = Field(None, ge=1, le=5)
    implementation_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    follow_up_required: Optional[bool] = None
    follow_up_date: Optional[datetime] = None
    follow_up_notes: Optional[str] = None


class RecommendationResponse(RecommendationBase):
    """Schema for recommendation response"""
    id: int
    user_id: int
    soil_health_card_id: Optional[int]
    leaf_analysis_id: Optional[int]
    weather_data_ids: Optional[str]
    
    # Fertilizer recommendations
    nitrogen_amount: Optional[float]
    phosphorus_amount: Optional[float]
    potassium_amount: Optional[float]
    fertilizer_type: Optional[str]
    fertilizer_application_method: Optional[str]
    
    # Pesticide recommendations
    pesticide_name: Optional[str]
    pesticide_dosage: Optional[float]
    pesticide_unit: Optional[str]
    target_pest_disease: Optional[str]
    
    # Timing recommendations
    recommended_date: Optional[datetime]
    optimal_time_start: Optional[datetime]
    optimal_time_end: Optional[datetime]
    weather_conditions_required: Optional[str]
    
    # Application details
    application_frequency: Optional[str]
    application_interval_days: Optional[int]
    total_applications: Optional[int]
    
    # Dosage and area
    dosage_per_acre: Optional[float]
    dosage_per_hectare: Optional[float]
    area_coverage: Optional[float]
    
    # Cost estimation
    estimated_cost: Optional[float]
    cost_currency: str
    cost_breakdown: Optional[str]
    
    # Expected outcomes
    expected_yield_increase: Optional[float]
    expected_quality_improvement: Optional[str]
    environmental_impact: Optional[str]
    
    # AI model information
    model_version: Optional[str]
    confidence_score: Optional[float]
    data_sources_used: Optional[str]
    
    # Priority and status
    priority: str
    urgency_days: Optional[int]
    status: str
    user_feedback: Optional[str]
    user_rating: Optional[int]
    implementation_date: Optional[datetime]
    completion_date: Optional[datetime]
    
    # Follow-up
    follow_up_required: bool
    follow_up_date: Optional[datetime]
    follow_up_notes: Optional[str]
    
    # Additional information
    precautions: Optional[str]
    alternative_options: Optional[str]
    references: Optional[str]
    
    # Localization
    language: str
    local_names: Optional[str]
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]
    expires_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class RecommendationListResponse(BaseModel):
    """Schema for recommendation list response"""
    recommendations: List[RecommendationResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
