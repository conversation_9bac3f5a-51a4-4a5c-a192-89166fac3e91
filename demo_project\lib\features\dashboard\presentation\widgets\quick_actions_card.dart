import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';

/// Quick actions card for dashboard
class QuickActionsCard extends StatelessWidget {
  const QuickActionsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: AppTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: MdiIcons.testTube,
                    label: 'Soil Test',
                    color: AppTheme.primaryBrown,
                    onTap: () => context.go(AppRouter.soilHealthForm),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: MdiIcons.leaf,
                    label: 'Leaf Analysis',
                    color: AppTheme.accentGreen,
                    onTap: () => context.go(AppRouter.leafAnalysisForm),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: MdiIcons.camera,
                    label: 'Take Photo',
                    color: AppTheme.infoBlue,
                    onTap: () => _showCameraOptions(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showCameraOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Take Photo',
              style: AppTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(MdiIcons.leaf, color: AppTheme.accentGreen),
              title: const Text('Leaf Photo for Analysis'),
              subtitle: const Text('Analyze leaf color for nitrogen deficiency'),
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.leafAnalysisForm);
              },
            ),
            ListTile(
              leading: Icon(MdiIcons.testTube, color: AppTheme.primaryBrown),
              title: const Text('Soil Sample Photo'),
              subtitle: const Text('Document soil conditions'),
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.soilHealthForm);
              },
            ),
            ListTile(
              leading: Icon(MdiIcons.sprout, color: AppTheme.primaryGreen),
              title: const Text('Crop Photo'),
              subtitle: const Text('Monitor crop growth and health'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to crop monitoring
              },
            ),
          ],
        ),
      ),
    );
  }
}
