"""
Crop and crop variety models
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class Crop(Base):
    """Crop model for storing crop information"""
    
    __tablename__ = "crops"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic crop information
    name = Column(String(100), nullable=False, unique=True)
    scientific_name = Column(String(255))
    common_names = Column(Text)  # JSON array of common names
    crop_family = Column(String(100))  # Cereals, Legumes, Vegetables, etc.
    crop_type = Column(String(50))  # Annual, Perennial, Biennial
    
    # Growing characteristics
    growing_season = Column(String(50))  # Kharif, <PERSON><PERSON>, Zaid
    maturity_days_min = Column(Integer)  # Minimum days to maturity
    maturity_days_max = Column(Integer)  # Maximum days to maturity
    
    # Soil requirements
    preferred_soil_type = Column(Text)  # JSON array of soil types
    ph_range_min = Column(Float)
    ph_range_max = Column(Float)
    
    # Climate requirements
    temperature_min = Column(Float)  # Minimum temperature (°C)
    temperature_max = Column(Float)  # Maximum temperature (°C)
    rainfall_min = Column(Float)  # Minimum rainfall (mm)
    rainfall_max = Column(Float)  # Maximum rainfall (mm)
    
    # Nutritional requirements (kg/ha)
    nitrogen_requirement = Column(Float)
    phosphorus_requirement = Column(Float)
    potassium_requirement = Column(Float)
    
    # Additional information
    description = Column(Text)
    cultivation_practices = Column(Text)  # JSON with cultivation guidelines
    common_pests = Column(Text)  # JSON array of common pests
    common_diseases = Column(Text)  # JSON array of common diseases
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    varieties = relationship("CropVariety", back_populates="crop")
    
    def __repr__(self):
        return f"<Crop(id={self.id}, name='{self.name}')>"


class CropVariety(Base):
    """Crop variety model for storing specific variety information"""
    
    __tablename__ = "crop_varieties"
    
    id = Column(Integer, primary_key=True, index=True)
    crop_id = Column(Integer, ForeignKey("crops.id"), nullable=False)
    
    # Variety information
    name = Column(String(100), nullable=False)
    variety_code = Column(String(50))
    breeder_name = Column(String(255))
    release_year = Column(Integer)
    
    # Characteristics
    maturity_days = Column(Integer)
    yield_potential = Column(Float)  # tons/hectare
    plant_height = Column(Float)  # cm
    
    # Resistance traits
    drought_tolerance = Column(String(20))  # low, medium, high
    disease_resistance = Column(Text)  # JSON array of diseases
    pest_resistance = Column(Text)  # JSON array of pests
    
    # Quality traits
    grain_quality = Column(String(50))
    protein_content = Column(Float)
    oil_content = Column(Float)
    
    # Adaptation
    suitable_regions = Column(Text)  # JSON array of regions
    suitable_seasons = Column(Text)  # JSON array of seasons
    
    # Additional information
    description = Column(Text)
    special_features = Column(Text)
    cultivation_notes = Column(Text)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    crop = relationship("Crop", back_populates="varieties")
    
    def __repr__(self):
        return f"<CropVariety(id={self.id}, name='{self.name}', crop_id={self.crop_id})>"
