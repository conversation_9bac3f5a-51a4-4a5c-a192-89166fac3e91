"""
Soil Health Card model for storing soil analysis data
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class SoilHealthCard(Base):
    """Soil Health Card model for storing soil analysis data"""
    
    __tablename__ = "soil_health_cards"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Basic information
    card_number = Column(String(50), unique=True, index=True)
    field_name = Column(String(255))
    sample_date = Column(DateTime(timezone=True))
    lab_name = Column(String(255))
    
    # Location information
    latitude = Column(Float)
    longitude = Column(Float)
    village = Column(String(255))
    district = Column(String(255))
    state = Column(String(255))
    
    # Soil properties
    soil_type = Column(String(100))  # Clay, Sandy, Loamy, etc.
    soil_texture = Column(String(100))
    
    # Chemical properties
    ph_level = Column(Float)  # pH value
    electrical_conductivity = Column(Float)  # EC in dS/m
    organic_carbon = Column(Float)  # OC in %
    
    # Macronutrients (kg/ha)
    nitrogen_n = Column(Float)  # Available Nitrogen
    phosphorus_p = Column(Float)  # Available Phosphorus
    potassium_k = Column(Float)  # Available Potassium
    
    # Secondary nutrients (kg/ha)
    sulfur_s = Column(Float)
    calcium_ca = Column(Float)
    magnesium_mg = Column(Float)
    
    # Micronutrients (ppm)
    iron_fe = Column(Float)
    manganese_mn = Column(Float)
    zinc_zn = Column(Float)
    copper_cu = Column(Float)
    boron_b = Column(Float)
    molybdenum_mo = Column(Float)
    
    # Additional properties
    cation_exchange_capacity = Column(Float)  # CEC in cmol/kg
    base_saturation = Column(Float)  # %
    
    # Recommendations from lab
    lab_recommendations = Column(Text)  # JSON string with lab recommendations
    
    # Status and notes
    status = Column(String(20), default="active")  # active, archived
    notes = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="soil_health_cards")
    recommendations = relationship("Recommendation", back_populates="soil_health_card")
    
    def __repr__(self):
        return f"<SoilHealthCard(id={self.id}, card_number='{self.card_number}', user_id={self.user_id})>"
