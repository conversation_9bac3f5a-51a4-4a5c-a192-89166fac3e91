import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';

/// Recommendations summary card for dashboard
class RecommendationsSummaryCard extends StatelessWidget {
  const RecommendationsSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.lightbulbOn,
                  color: AppTheme.warningOrange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Active Recommendations',
                  style: AppTheme.titleMedium,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => context.go(AppRouter.recommendations),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildRecommendationItem(
              icon: MdiIcons.testTube,
              title: 'Nitrogen Fertilizer Application',
              description: 'Apply 60 kg/ha of Urea in Field A',
              priority: 'High',
              priorityColor: AppTheme.errorRed,
            ),
            const SizedBox(height: 12),
            _buildRecommendationItem(
              icon: MdiIcons.weatherRainy,
              title: 'Irrigation Scheduling',
              description: 'Irrigate Field B in next 2 days',
              priority: 'Medium',
              priorityColor: AppTheme.warningOrange,
            ),
            const SizedBox(height: 12),
            _buildRecommendationItem(
              icon: MdiIcons.bug,
              title: 'Pest Monitoring',
              description: 'Check for aphids in Field C',
              priority: 'Low',
              priorityColor: AppTheme.successGreen,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationItem({
    required IconData icon,
    required String title,
    required String description,
    required String priority,
    required Color priorityColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: priorityColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: priorityColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: priorityColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              priority,
              style: AppTheme.labelSmall.copyWith(
                color: priorityColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
