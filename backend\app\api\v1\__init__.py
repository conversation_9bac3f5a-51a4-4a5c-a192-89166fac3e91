"""
API v1 package
"""
from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router
from .soil_health import router as soil_health_router
from .weather import router as weather_router
from .leaf_analysis import router as leaf_analysis_router
from .recommendations import router as recommendations_router
from .crops import router as crops_router

api_router = APIRouter()

# Include all routers
api_router.include_router(auth_router, prefix="/auth", tags=["authentication"])
api_router.include_router(users_router, prefix="/users", tags=["users"])
api_router.include_router(soil_health_router, prefix="/soil-health", tags=["soil-health"])
api_router.include_router(weather_router, prefix="/weather", tags=["weather"])
api_router.include_router(leaf_analysis_router, prefix="/leaf-analysis", tags=["leaf-analysis"])
api_router.include_router(recommendations_router, prefix="/recommendations", tags=["recommendations"])
api_router.include_router(crops_router, prefix="/crops", tags=["crops"])
