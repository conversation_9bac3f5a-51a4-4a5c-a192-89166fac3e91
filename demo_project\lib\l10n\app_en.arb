{"@@locale": "en", "appName": "FarmSmart AI", "@appName": {"description": "The name of the application"}, "welcome": "Welcome", "welcomeBack": "Welcome back, {name}!", "@welcomeBack": {"description": "Welcome message with user name", "placeholders": {"name": {"type": "String", "example": "<PERSON>"}}}, "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "fullName": "Full Name", "phoneNumber": "Phone Number", "farmName": "Farm Name", "farmLocation": "Farm Location", "farmSize": "Farm Size", "acres": "acres", "signIn": "Sign In", "signUp": "Sign Up", "forgotPassword": "Forgot Password?", "rememberMe": "Remember me", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "soilHealth": "Soil Health", "weather": "Weather", "leafAnalysis": "Leaf Analysis", "recommendations": "Recommendations", "crops": "Crops", "quickOverview": "Quick Overview", "quickActions": "Quick Actions", "weatherSummary": "Weather Summary", "recentActivities": "Recent Activities", "activeRecommendations": "Active Recommendations", "soilHealthCards": "Soil Health Cards", "temperature": "Temperature", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "rainfall": "Rainfall", "wind": "Wind", "forecast": "7-Day Forecast", "today": "Today", "tomorrow": "Tomorrow", "notifications": "Notifications", "markAllAsRead": "Mark all as read", "weatherAlert": "<PERSON> Alert", "fertilizerRecommendation": "Fertilizer Recommendation", "analysisComplete": "Analysis Complete", "soilTest": "Soil Test", "takePhoto": "Take Photo", "camera": "Camera", "gallery": "Gallery", "addSoilCard": "Add Soil Card", "editProfile": "Edit Profile", "personalInformation": "Personal Information", "farmInformation": "Farm Information", "accountSettings": "Account <PERSON><PERSON>", "appSettings": "App Settings", "dataStorage": "Data & Storage", "support": "Support", "theme": "Theme", "language": "Language", "changePassword": "Change Password", "syncData": "Sync Data", "exportData": "Export Data", "clearCache": "<PERSON>ache", "helpFaq": "Help & FAQ", "contactSupport": "Contact Support", "about": "About", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "update": "Update", "refresh": "Refresh", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "notSpecified": "Not specified", "fieldName": "Field Name", "phLevel": "pH Level", "nitrogen": "Nitrogen (N) kg/ha", "phosphorus": "Phosphorus (P) kg/ha", "potassium": "Potassium (K) kg/ha", "cropType": "Crop Type", "growthStage": "Growth Stage", "lccReading": "LCC Reading (1-6)", "analyze": "Analyze", "results": "Results", "nitrogenStatus": "Nitrogen Status", "phosphorusStatus": "Phosphorus Status", "potassiumStatus": "Potassium Status", "overallHealth": "Overall Health", "normal": "Normal", "low": "Low", "medium": "Medium", "high": "High", "optimal": "Optimal", "good": "Good", "poor": "Poor", "excellent": "Excellent", "deficiency": "Deficiency", "mild": "Mild", "moderate": "Moderate", "severe": "Severe", "applyFertilizer": "Apply Fertilizer", "irrigation": "Irrigation", "pestControl": "Pest Control", "monitoring": "Monitoring", "harvest": "Harvest", "priority": "Priority", "urgent": "<PERSON><PERSON>", "immediate": "Immediate", "days": "days", "weeks": "weeks", "hours": "hours", "minutes": "minutes", "ago": "ago", "next": "Next", "previous": "Previous", "viewAll": "View All", "viewDetails": "View Details", "markDone": "<PERSON>", "dismiss": "<PERSON><PERSON><PERSON>", "retry": "Retry", "continueButton": "Continue", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "light": "Light", "dark": "Dark", "system": "System", "english": "English", "tamil": "Tamil", "malayalam": "Malayalam", "farmer": "<PERSON>", "expert": "Expert", "advisor": "Advisor", "student": "Student", "researcher": "Researcher", "loginSuccessful": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "registrationSuccessful": "Registration successful", "registrationFailed": "Registration failed", "profileUpdated": "Profile updated successfully", "passwordChanged": "Password changed successfully", "logoutConfirm": "Are you sure you want to logout?", "deleteConfirm": "Are you sure you want to delete this item?", "clearCacheConfirm": "This will clear all cached data. Continue?", "networkError": "Network error. Please check your connection.", "invalidCredentials": "Invalid username or password", "passwordTooShort": "Password must be at least 6 characters", "emailInvalid": "Please enter a valid email address", "fieldRequired": "This field is required", "farmHealthy": "Your farm is looking healthy! Check out the latest recommendations below.", "goodConditions": "Good conditions for fertilizer application", "rainExpected": "Rain expected in next 24 hours", "nitrogenDeficiency": "Nitrogen deficiency detected in Field A", "soilAnalysisReady": "Soil health card analysis ready", "captureLeafImage": "Capture Leaf Image", "takePhotoInstruction": "Take a clear photo of the leaf for AI analysis", "precisionAgriculture": "Precision Agriculture for Smart Farming", "initializing": "Initializing...", "initializationError": "Failed to initialize the app. Please restart the application.", "basicInformation": "Basic Information", "startWithDetails": "Let's start with your basic details", "acceptTerms": "I accept the Terms and Conditions", "readTerms": "Read Terms and Conditions", "createAccountButton": "Create Account", "back": "Back", "emailVerification": "Please check your email for verification instructions.", "continueToLogin": "Continue to Login", "passwordResetSent": "Password reset link sent to your email", "enterEmailForReset": "Enter your email address and we'll send you a link to reset your password.", "sendLink": "Send Link", "resetPassword": "Reset Password", "currentPassword": "Current Password", "newPassword": "New Password", "changePasswordSuccess": "Password changed successfully", "changePasswordFailed": "Failed to change password", "profilePicture": "Profile Picture", "changeProfilePicture": "Change Profile Picture", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "unsavedChanges": "You have unsaved changes. Do you want to save them?", "statistics": "Statistics", "soilTests": "Soil Tests", "leafAnalyses": "Leaf Analyses", "totalRecommendations": "Recommendations", "appVersion": "App Version", "buildNumber": "Build Number", "developer": "Developer", "copyright": "© 2024 FarmSmart AI. All rights reserved.", "appDescription": "Precision Agriculture App for Soil Health & Crop Optimization", "appLongDescription": "Integrates Soil Health Card, weather data, and Leaf Color Chart for sustainable farming recommendations.", "cacheCleared": "<PERSON><PERSON> cleared successfully", "dataSynced": "Data synced successfully", "dataExported": "Data exported successfully", "notificationsEnabled": "Notifications enabled", "notificationsDisabled": "Notifications disabled", "themeChanged": "Theme changed successfully", "languageChanged": "Language changed successfully", "restartRequired": "Please restart the app to apply language changes", "offlineMode": "Offline Mode", "onlineMode": "Online Mode", "syncPending": "Sync pending", "lastSynced": "Last synced", "never": "Never", "justNow": "Just now"}