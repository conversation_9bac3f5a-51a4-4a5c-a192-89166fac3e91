// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Malayalam (`ml`).
class AppLocalizationsMl extends AppLocalizations {
  AppLocalizationsMl([String locale = 'ml']) : super(locale);

  @override
  String get appName => 'ഫാം സ്മാർട്ട് AI';

  @override
  String get welcome => 'സ്വാഗതം';

  @override
  String welcomeBack(String name) {
    return 'തിരികെ സ്വാഗതം, $name!';
  }

  @override
  String get login => 'ലോഗിൻ';

  @override
  String get register => 'രജിസ്റ്റർ';

  @override
  String get logout => 'ലോഗൗട്ട്';

  @override
  String get email => 'ഇമെയിൽ';

  @override
  String get password => 'പാസ്‌വേഡ്';

  @override
  String get confirmPassword => 'പാസ്‌വേഡ് സ്ഥിരീകരിക്കുക';

  @override
  String get username => 'ഉപയോക്തൃനാമം';

  @override
  String get fullName => 'പൂർണ്ണ നാമം';

  @override
  String get phoneNumber => 'ഫോൺ നമ്പർ';

  @override
  String get farmName => 'ഫാമിന്റെ പേര്';

  @override
  String get farmLocation => 'ഫാമിന്റെ സ്ഥലം';

  @override
  String get farmSize => 'ഫാമിന്റെ വലുപ്പം';

  @override
  String get acres => 'ഏക്കർ';

  @override
  String get signIn => 'സൈൻ ഇൻ';

  @override
  String get signUp => 'സൈൻ അപ്പ്';

  @override
  String get forgotPassword => 'പാസ്‌വേഡ് മറന്നോ?';

  @override
  String get rememberMe => 'എന്നെ ഓർക്കുക';

  @override
  String get createAccount => 'അക്കൗണ്ട് സൃഷ്ടിക്കുക';

  @override
  String get alreadyHaveAccount => 'ഇതിനകം അക്കൗണ്ട് ഉണ്ടോ?';

  @override
  String get dontHaveAccount => 'അക്കൗണ്ട് ഇല്ലേ?';

  @override
  String get dashboard => 'ഡാഷ്‌ബോർഡ്';

  @override
  String get profile => 'പ്രൊഫൈൽ';

  @override
  String get settings => 'ക്രമീകരണങ്ങൾ';

  @override
  String get soilHealth => 'മണ്ണിന്റെ ആരോഗ്യം';

  @override
  String get weather => 'കാലാവസ്ഥ';

  @override
  String get leafAnalysis => 'ഇല വിശകലനം';

  @override
  String get recommendations => 'ശുപാർശകൾ';

  @override
  String get crops => 'വിളകൾ';

  @override
  String get quickOverview => 'ദ്രുത അവലോകനം';

  @override
  String get quickActions => 'ദ്രുത പ്രവർത്തനങ്ങൾ';

  @override
  String get weatherSummary => 'കാലാവസ്ഥാ സംഗ്രഹം';

  @override
  String get recentActivities => 'സമീപകാല പ്രവർത്തനങ്ങൾ';

  @override
  String get activeRecommendations => 'സജീവ ശുപാർശകൾ';

  @override
  String get soilHealthCards => 'മണ്ണിന്റെ ആരോഗ്യ കാർഡുകൾ';

  @override
  String get temperature => 'താപനില';

  @override
  String get humidity => 'ആർദ്രത';

  @override
  String get rainfall => 'മഴ';

  @override
  String get wind => 'കാറ്റ്';

  @override
  String get forecast => '7-ദിവസ പ്രവചനം';

  @override
  String get today => 'ഇന്ന്';

  @override
  String get tomorrow => 'നാളെ';

  @override
  String get notifications => 'അറിയിപ്പുകൾ';

  @override
  String get markAllAsRead => 'എല്ലാം വായിച്ചതായി അടയാളപ്പെടുത്തുക';

  @override
  String get weatherAlert => 'കാലാവസ്ഥാ മുന്നറിയിപ്പ്';

  @override
  String get fertilizerRecommendation => 'വളം ശുപാർശ';

  @override
  String get analysisComplete => 'വിശകലനം പൂർത്തിയായി';

  @override
  String get soilTest => 'മണ്ണ് പരിശോധന';

  @override
  String get takePhoto => 'ഫോട്ടോ എടുക്കുക';

  @override
  String get camera => 'ക്യാമറ';

  @override
  String get gallery => 'ഗാലറി';

  @override
  String get addSoilCard => 'മണ്ണ് കാർഡ് ചേർക്കുക';

  @override
  String get editProfile => 'പ്രൊഫൈൽ എഡിറ്റ് ചെയ്യുക';

  @override
  String get personalInformation => 'വ്യക്തിഗത വിവരങ്ങൾ';

  @override
  String get farmInformation => 'ഫാം വിവരങ്ങൾ';

  @override
  String get accountSettings => 'അക്കൗണ്ട് ക്രമീകരണങ്ങൾ';

  @override
  String get appSettings => 'ആപ്പ് ക്രമീകരണങ്ങൾ';

  @override
  String get dataStorage => 'ഡാറ്റയും സംഭരണവും';

  @override
  String get support => 'പിന്തുണ';

  @override
  String get theme => 'തീം';

  @override
  String get language => 'ഭാഷ';

  @override
  String get changePassword => 'പാസ്‌വേഡ് മാറ്റുക';

  @override
  String get syncData => 'ഡാറ്റ സിങ്ക് ചെയ്യുക';

  @override
  String get exportData => 'ഡാറ്റ എക്സ്പോർട്ട് ചെയ്യുക';

  @override
  String get clearCache => 'കാഷെ മായ്ക്കുക';

  @override
  String get helpFaq => 'സഹായവും FAQ';

  @override
  String get contactSupport => 'പിന്തുണയുമായി ബന്ധപ്പെടുക';

  @override
  String get about => 'കുറിച്ച്';

  @override
  String get save => 'സേവ് ചെയ്യുക';

  @override
  String get cancel => 'റദ്ദാക്കുക';

  @override
  String get delete => 'ഇല്ലാതാക്കുക';

  @override
  String get edit => 'എഡിറ്റ് ചെയ്യുക';

  @override
  String get add => 'ചേർക്കുക';

  @override
  String get update => 'അപ്ഡേറ്റ് ചെയ്യുക';

  @override
  String get refresh => 'പുതുക്കുക';

  @override
  String get loading => 'ലോഡിംഗ്...';

  @override
  String get error => 'പിശക്';

  @override
  String get success => 'വിജയം';

  @override
  String get warning => 'മുന്നറിയിപ്പ്';

  @override
  String get info => 'വിവരം';

  @override
  String get required => 'ആവശ്യമായ';

  @override
  String get optional => 'ഓപ്ഷണൽ';

  @override
  String get notSpecified => 'വ്യക്തമാക്കിയിട്ടില്ല';

  @override
  String get fieldName => 'വയലിന്റെ പേര്';

  @override
  String get phLevel => 'pH ലെവൽ';

  @override
  String get nitrogen => 'നൈട്രജൻ (N) kg/ha';

  @override
  String get phosphorus => 'ഫോസ്ഫറസ് (P) kg/ha';

  @override
  String get potassium => 'പൊട്ടാസ്യം (K) kg/ha';

  @override
  String get cropType => 'വിള തരം';

  @override
  String get growthStage => 'വളർച്ചാ ഘട്ടം';

  @override
  String get lccReading => 'LCC റീഡിംഗ് (1-6)';

  @override
  String get analyze => 'വിശകലനം ചെയ്യുക';

  @override
  String get results => 'ഫലങ്ങൾ';

  @override
  String get nitrogenStatus => 'നൈട്രജൻ നില';

  @override
  String get phosphorusStatus => 'ഫോസ്ഫറസ് നില';

  @override
  String get potassiumStatus => 'പൊട്ടാസ്യം നില';

  @override
  String get overallHealth => 'മൊത്തത്തിലുള്ള ആരോഗ്യം';

  @override
  String get normal => 'സാധാരണ';

  @override
  String get low => 'കുറവ്';

  @override
  String get medium => 'ഇടത്തരം';

  @override
  String get high => 'ഉയർന്ന';

  @override
  String get optimal => 'ഒപ്റ്റിമൽ';

  @override
  String get good => 'നല്ല';

  @override
  String get poor => 'മോശം';

  @override
  String get excellent => 'മികച്ച';

  @override
  String get deficiency => 'കുറവ്';

  @override
  String get mild => 'നേരിയ';

  @override
  String get moderate => 'മിതമായ';

  @override
  String get severe => 'കഠിനമായ';

  @override
  String get applyFertilizer => 'വളം പ്രയോഗിക്കുക';

  @override
  String get irrigation => 'ജലസേചനം';

  @override
  String get pestControl => 'കീട നിയന്ത്രണം';

  @override
  String get monitoring => 'നിരീക്ഷണം';

  @override
  String get harvest => 'വിളവെടുപ്പ്';

  @override
  String get priority => 'മുൻഗണന';

  @override
  String get urgent => 'അടിയന്തിര';

  @override
  String get immediate => 'ഉടനടി';

  @override
  String get days => 'ദിവസങ്ങൾ';

  @override
  String get weeks => 'ആഴ്ചകൾ';

  @override
  String get hours => 'മണിക്കൂറുകൾ';

  @override
  String get minutes => 'മിനിറ്റുകൾ';

  @override
  String get ago => 'മുമ്പ്';

  @override
  String get next => 'അടുത്തത്';

  @override
  String get previous => 'മുമ്പത്തെ';

  @override
  String get viewAll => 'എല്ലാം കാണുക';

  @override
  String get viewDetails => 'വിശദാംശങ്ങൾ കാണുക';

  @override
  String get markDone => 'പൂർത്തിയായതായി അടയാളപ്പെടുത്തുക';

  @override
  String get dismiss => 'നിരസിക്കുക';

  @override
  String get retry => 'വീണ്ടും ശ്രമിക്കുക';

  @override
  String get continueButton => 'തുടരുക';

  @override
  String get close => 'അടയ്ക്കുക';

  @override
  String get ok => 'ശരി';

  @override
  String get yes => 'അതെ';

  @override
  String get no => 'ഇല്ല';

  @override
  String get light => 'വെളിച്ചം';

  @override
  String get dark => 'ഇരുട്ട്';

  @override
  String get system => 'സിസ്റ്റം';

  @override
  String get english => 'ഇംഗ്ലീഷ്';

  @override
  String get tamil => 'തമിഴ്';

  @override
  String get malayalam => 'മലയാളം';

  @override
  String get farmer => 'കർഷകൻ';

  @override
  String get expert => 'വിദഗ്ധൻ';

  @override
  String get advisor => 'ഉപദേശകൻ';

  @override
  String get student => 'വിദ്യാർത്ഥി';

  @override
  String get researcher => 'ഗവേഷകൻ';

  @override
  String get loginSuccessful => 'ലോഗിൻ വിജയകരം';

  @override
  String get loginFailed => 'ലോഗിൻ പരാജയപ്പെട്ടു';

  @override
  String get registrationSuccessful => 'രജിസ്ട്രേഷൻ വിജയകരം';

  @override
  String get registrationFailed => 'രജിസ്ട്രേഷൻ പരാജയപ്പെട്ടു';

  @override
  String get profileUpdated => 'പ്രൊഫൈൽ വിജയകരമായി അപ്ഡേറ്റ് ചെയ്തു';

  @override
  String get passwordChanged => 'പാസ്‌വേഡ് വിജയകരമായി മാറ്റി';

  @override
  String get logoutConfirm =>
      'നിങ്ങൾ തീർച്ചയായും ലോഗൗട്ട് ചെയ്യാൻ ആഗ്രഹിക്കുന്നുണ്ടോ?';

  @override
  String get deleteConfirm =>
      'ഈ ഇനം തീർച്ചയായും ഇല്ലാതാക്കാൻ ആഗ്രഹിക്കുന്നുണ്ടോ?';

  @override
  String get clearCacheConfirm => 'ഇത് എല്ലാ കാഷെ ഡാറ്റയും മായ്ക്കും. തുടരണോ?';

  @override
  String get networkError =>
      'നെറ്റ്‌വർക്ക് പിശക്. നിങ്ങളുടെ കണക്ഷൻ പരിശോധിക്കുക.';

  @override
  String get invalidCredentials => 'തെറ്റായ ഉപയോക്തൃനാമം അല്ലെങ്കിൽ പാസ്‌വേഡ്';

  @override
  String get passwordTooShort => 'പാസ്‌വേഡ് കുറഞ്ഞത് 6 പ്രതീകങ്ങൾ ആയിരിക്കണം';

  @override
  String get emailInvalid => 'സാധുവായ ഇമെയിൽ വിലാസം നൽകുക';

  @override
  String get fieldRequired => 'ഈ ഫീൽഡ് ആവശ്യമാണ്';

  @override
  String get farmHealthy =>
      'നിങ്ങളുടെ ഫാം ആരോഗ്യകരമായി കാണപ്പെടുന്നു! താഴെയുള്ള ഏറ്റവും പുതിയ ശുപാർശകൾ പരിശോധിക്കുക.';

  @override
  String get goodConditions => 'വളം പ്രയോഗത്തിന് നല്ല അവസ്ഥകൾ';

  @override
  String get rainExpected => 'അടുത്ത 24 മണിക്കൂറിൽ മഴ പ്രതീക്ഷിക്കുന്നു';

  @override
  String get nitrogenDeficiency => 'ഫീൽഡ് A യിൽ നൈട്രജൻ കുറവ് കണ്ടെത്തി';

  @override
  String get soilAnalysisReady => 'മണ്ണിന്റെ ആരോഗ്യ കാർഡ് വിശകലനം തയ്യാർ';

  @override
  String get captureLeafImage => 'ഇല ചിത്രം പകർത്തുക';

  @override
  String get takePhotoInstruction =>
      'AI വിശകലനത്തിനായി ഇലയുടെ വ്യക്തമായ ഫോട്ടോ എടുക്കുക';

  @override
  String get precisionAgriculture => 'സ്മാർട്ട് കൃഷിക്കുള്ള കൃത്യമായ കൃഷി';

  @override
  String get initializing => 'ആരംഭിക്കുന്നു...';

  @override
  String get initializationError =>
      'ആപ്പ് ആരംഭിക്കാൻ കഴിഞ്ഞില്ല. ആപ്ലിക്കേഷൻ പുനരാരംഭിക്കുക.';

  @override
  String get basicInformation => 'അടിസ്ഥാന വിവരങ്ങൾ';

  @override
  String get startWithDetails =>
      'നിങ്ങളുടെ അടിസ്ഥാന വിശദാംശങ്ങളിൽ നിന്ന് ആരംഭിക്കാം';

  @override
  String get acceptTerms => 'ഞാൻ നിബന്ധനകളും വ്യവസ്ഥകളും അംഗീകരിക്കുന്നു';

  @override
  String get readTerms => 'നിബന്ധനകളും വ്യവസ്ഥകളും വായിക്കുക';

  @override
  String get createAccountButton => 'അക്കൗണ്ട് സൃഷ്ടിക്കുക';

  @override
  String get back => 'പിന്നിലേക്ക്';

  @override
  String get emailVerification =>
      'സ്ഥിരീകരണ നിർദ്ദേശങ്ങൾക്കായി നിങ്ങളുടെ ഇമെയിൽ പരിശോധിക്കുക.';

  @override
  String get continueToLogin => 'ലോഗിനിലേക്ക് തുടരുക';

  @override
  String get passwordResetSent =>
      'പാസ്‌വേഡ് റീസെറ്റ് ലിങ്ക് നിങ്ങളുടെ ഇമെയിലിലേക്ക് അയച്ചു';

  @override
  String get enterEmailForReset =>
      'നിങ്ങളുടെ ഇമെയിൽ വിലാസം നൽകുക, പാസ്‌വേഡ് റീസെറ്റ് ചെയ്യാൻ ഞങ്ങൾ ഒരു ലിങ്ക് അയയ്ക്കും.';

  @override
  String get sendLink => 'ലിങ്ക് അയയ്ക്കുക';

  @override
  String get resetPassword => 'പാസ്‌വേഡ് റീസെറ്റ് ചെയ്യുക';

  @override
  String get currentPassword => 'നിലവിലെ പാസ്‌വേഡ്';

  @override
  String get newPassword => 'പുതിയ പാസ്‌വേഡ്';

  @override
  String get changePasswordSuccess => 'പാസ്‌വേഡ് വിജയകരമായി മാറ്റി';

  @override
  String get changePasswordFailed => 'പാസ്‌വേഡ് മാറ്റാൻ കഴിഞ്ഞില്ല';

  @override
  String get profilePicture => 'പ്രൊഫൈൽ ചിത്രം';

  @override
  String get changeProfilePicture => 'പ്രൊഫൈൽ ചിത്രം മാറ്റുക';

  @override
  String get saveChanges => 'മാറ്റങ്ങൾ സേവ് ചെയ്യുക';

  @override
  String get discardChanges => 'മാറ്റങ്ങൾ നിരസിക്കുക';

  @override
  String get unsavedChanges =>
      'നിങ്ങൾക്ക് സേവ് ചെയ്യാത്ത മാറ്റങ്ങളുണ്ട്. അവ സേവ് ചെയ്യാൻ ആഗ്രഹിക്കുന്നുണ്ടോ?';

  @override
  String get statistics => 'സ്ഥിതിവിവരക്കണക്കുകൾ';

  @override
  String get soilTests => 'മണ്ണ് പരിശോധനകൾ';

  @override
  String get leafAnalyses => 'ഇല വിശകലനങ്ങൾ';

  @override
  String get totalRecommendations => 'ശുപാർശകൾ';

  @override
  String get appVersion => 'ആപ്പ് പതിപ്പ്';

  @override
  String get buildNumber => 'ബിൽഡ് നമ്പർ';

  @override
  String get developer => 'ഡെവലപ്പർ';

  @override
  String get copyright =>
      '© 2024 ഫാം സ്മാർട്ട് AI. എല്ലാ അവകാശങ്ങളും സംരക്ഷിതം.';

  @override
  String get appDescription =>
      'മണ്ണിന്റെ ആരോഗ്യത്തിനും വിള ഒപ്റ്റിമൈസേഷനുമുള്ള കൃത്യമായ കൃഷി ആപ്പ്';

  @override
  String get appLongDescription =>
      'സുസ്ഥിര കൃഷി ശുപാർശകൾക്കായി മണ്ണിന്റെ ആരോഗ്യ കാർഡ്, കാലാവസ്ഥാ ഡാറ്റ, ഇല വർണ്ണ ചാർട്ട് എന്നിവ സംയോജിപ്പിക്കുന്നു.';

  @override
  String get cacheCleared => 'കാഷെ വിജയകരമായി മായ്ച്ചു';

  @override
  String get dataSynced => 'ഡാറ്റ വിജയകരമായി സിങ്ക് ചെയ്തു';

  @override
  String get dataExported => 'ഡാറ്റ വിജയകരമായി എക്സ്പോർട്ട് ചെയ്തു';

  @override
  String get notificationsEnabled => 'അറിയിപ്പുകൾ പ്രവർത്തനക്ഷമമാക്കി';

  @override
  String get notificationsDisabled => 'അറിയിപ്പുകൾ പ്രവർത്തനരഹിതമാക്കി';

  @override
  String get themeChanged => 'തീം വിജയകരമായി മാറ്റി';

  @override
  String get languageChanged => 'ഭാഷ വിജയകരമായി മാറ്റി';

  @override
  String get restartRequired =>
      'ഭാഷാ മാറ്റങ്ങൾ പ്രയോഗിക്കാൻ ആപ്പ് പുനരാരംഭിക്കുക';

  @override
  String get offlineMode => 'ഓഫ്‌ലൈൻ മോഡ്';

  @override
  String get onlineMode => 'ഓൺലൈൻ മോഡ്';

  @override
  String get syncPending => 'സിങ്ക് തീർപ്പുകൽപ്പിക്കാത്തത്';

  @override
  String get lastSynced => 'അവസാനമായി സിങ്ക് ചെയ്തത്';

  @override
  String get never => 'ഒരിക്കലും';

  @override
  String get justNow => 'ഇപ്പോൾ തന്നെ';
}
