"""
Soil health endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.schemas.soil_health import SoilHealthCardCreate, SoilHealthCardUpdate, SoilHealthCardResponse
from app.services.soil_health_service import SoilHealthService
from app.core.deps import get_current_active_user
from app.models.user import User

router = APIRouter()


@router.post("/", response_model=SoilHealthCardResponse, status_code=status.HTTP_201_CREATED)
def create_soil_health_card(
    card_data: SoilHealthCardCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new soil health card"""
    soil_service = SoilHealthService(db)
    
    try:
        card = soil_service.create_soil_health_card(current_user.id, card_data)
        return card
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating soil health card"
        )


@router.get("/", response_model=List[SoilHealthCardResponse])
def get_user_soil_health_cards(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all soil health cards for current user"""
    soil_service = SoilHealthService(db)
    
    cards = soil_service.get_user_soil_health_cards(current_user.id, skip, limit)
    return cards


@router.get("/{card_id}", response_model=SoilHealthCardResponse)
def get_soil_health_card(
    card_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific soil health card"""
    soil_service = SoilHealthService(db)
    
    card = soil_service.get_soil_health_card(card_id, current_user.id)
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil health card not found"
        )
    
    return card


@router.put("/{card_id}", response_model=SoilHealthCardResponse)
def update_soil_health_card(
    card_id: int,
    card_data: SoilHealthCardUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update soil health card"""
    soil_service = SoilHealthService(db)
    
    updated_card = soil_service.update_soil_health_card(card_id, current_user.id, card_data)
    if not updated_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil health card not found"
        )
    
    return updated_card


@router.get("/{card_id}/analysis")
def analyze_soil_health(
    card_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get soil health analysis for a specific card"""
    soil_service = SoilHealthService(db)
    
    card = soil_service.get_soil_health_card(card_id, current_user.id)
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil health card not found"
        )
    
    analysis = soil_service.analyze_soil_health(card)
    return analysis


@router.get("/{card_id}/recommendations")
def get_fertilizer_recommendations(
    card_id: int,
    crop_type: str = Query("general", description="Type of crop for specific recommendations"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get fertilizer recommendations based on soil health card"""
    soil_service = SoilHealthService(db)
    
    card = soil_service.get_soil_health_card(card_id, current_user.id)
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil health card not found"
        )
    
    recommendations = soil_service.generate_fertilizer_recommendations(card, crop_type)
    return {
        "card_id": card_id,
        "crop_type": crop_type,
        "recommendations": recommendations
    }


@router.delete("/{card_id}")
def delete_soil_health_card(
    card_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Archive soil health card (soft delete)"""
    soil_service = SoilHealthService(db)
    
    card = soil_service.get_soil_health_card(card_id, current_user.id)
    if not card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil health card not found"
        )
    
    # Update status to archived instead of deleting
    from app.schemas.soil_health import SoilHealthCardUpdate
    update_data = SoilHealthCardUpdate(status="archived")
    soil_service.update_soil_health_card(card_id, current_user.id, update_data)
    
    return {"message": "Soil health card archived successfully"}
