"""
Recommendations endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.schemas.recommendation import RecommendationCreate, RecommendationUpdate, RecommendationResponse, RecommendationListResponse
from app.services.recommendation_service import RecommendationService
from app.core.deps import get_current_active_user
from app.models.user import User

router = APIRouter()


@router.post("/", response_model=RecommendationResponse, status_code=status.HTTP_201_CREATED)
def create_recommendation(
    recommendation_data: RecommendationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new recommendation"""
    recommendation_service = RecommendationService(db)
    
    try:
        recommendation = recommendation_service.create_recommendation(current_user.id, recommendation_data)
        return recommendation
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating recommendation"
        )


@router.get("/", response_model=RecommendationListResponse)
def get_user_recommendations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    recommendation_type: str = Query(None),
    status_filter: str = Query(None),
    priority: str = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all recommendations for current user with filtering"""
    recommendation_service = RecommendationService(db)
    
    # Get recommendations with basic pagination
    recommendations = recommendation_service.get_user_recommendations(current_user.id, skip, limit)
    
    # Apply filters if provided
    if recommendation_type:
        recommendations = [r for r in recommendations if r.recommendation_type.value == recommendation_type]
    
    if status_filter:
        recommendations = [r for r in recommendations if r.status == status_filter]
    
    if priority:
        recommendations = [r for r in recommendations if r.priority == priority]
    
    total = len(recommendations)
    has_next = skip + limit < total
    has_prev = skip > 0
    
    return {
        "recommendations": recommendations,
        "total": total,
        "page": (skip // limit) + 1,
        "per_page": limit,
        "has_next": has_next,
        "has_prev": has_prev
    }


@router.get("/{recommendation_id}", response_model=RecommendationResponse)
def get_recommendation(
    recommendation_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific recommendation"""
    recommendation_service = RecommendationService(db)
    
    recommendation = recommendation_service.get_recommendation(recommendation_id, current_user.id)
    if not recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    return recommendation


@router.put("/{recommendation_id}", response_model=RecommendationResponse)
def update_recommendation(
    recommendation_id: int,
    recommendation_data: RecommendationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update recommendation (mainly for user feedback)"""
    recommendation_service = RecommendationService(db)
    
    updated_recommendation = recommendation_service.update_recommendation(
        recommendation_id, current_user.id, recommendation_data
    )
    if not updated_recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    return updated_recommendation


@router.post("/generate")
async def generate_comprehensive_recommendations(
    crop_type: str = Query(None, description="Specific crop type for targeted recommendations"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate comprehensive recommendations using all available data sources"""
    recommendation_service = RecommendationService(db)
    
    try:
        recommendations = await recommendation_service.generate_comprehensive_recommendations(
            current_user.id, crop_type
        )
        
        return {
            "message": "Recommendations generated successfully",
            "total_recommendations": len(recommendations),
            "crop_type": crop_type,
            "recommendations": recommendations
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating recommendations: {str(e)}"
        )


@router.post("/{recommendation_id}/accept")
def accept_recommendation(
    recommendation_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Accept a recommendation"""
    recommendation_service = RecommendationService(db)
    
    from app.schemas.recommendation import RecommendationUpdate
    update_data = RecommendationUpdate(status="accepted")
    
    updated_recommendation = recommendation_service.update_recommendation(
        recommendation_id, current_user.id, update_data
    )
    if not updated_recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    return {"message": "Recommendation accepted successfully"}


@router.post("/{recommendation_id}/reject")
def reject_recommendation(
    recommendation_id: int,
    feedback: str = Query(None, description="Optional feedback for rejection"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Reject a recommendation"""
    recommendation_service = RecommendationService(db)
    
    from app.schemas.recommendation import RecommendationUpdate
    update_data = RecommendationUpdate(
        status="rejected",
        user_feedback=feedback
    )
    
    updated_recommendation = recommendation_service.update_recommendation(
        recommendation_id, current_user.id, update_data
    )
    if not updated_recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    return {"message": "Recommendation rejected successfully"}


@router.post("/{recommendation_id}/complete")
def complete_recommendation(
    recommendation_id: int,
    rating: int = Query(None, ge=1, le=5, description="User rating (1-5)"),
    feedback: str = Query(None, description="Optional feedback"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark recommendation as completed"""
    recommendation_service = RecommendationService(db)
    
    from app.schemas.recommendation import RecommendationUpdate
    from datetime import datetime
    
    update_data = RecommendationUpdate(
        status="completed",
        completion_date=datetime.utcnow(),
        user_rating=rating,
        user_feedback=feedback
    )
    
    updated_recommendation = recommendation_service.update_recommendation(
        recommendation_id, current_user.id, update_data
    )
    if not updated_recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    return {"message": "Recommendation marked as completed successfully"}


@router.get("/stats/summary")
def get_recommendation_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get recommendation statistics for current user"""
    recommendation_service = RecommendationService(db)
    
    all_recommendations = recommendation_service.get_user_recommendations(current_user.id, 0, 10000)
    
    stats = {
        "total_recommendations": len(all_recommendations),
        "pending": len([r for r in all_recommendations if r.status == "pending"]),
        "accepted": len([r for r in all_recommendations if r.status == "accepted"]),
        "rejected": len([r for r in all_recommendations if r.status == "rejected"]),
        "completed": len([r for r in all_recommendations if r.status == "completed"]),
        "by_type": {},
        "by_priority": {},
        "average_rating": 0
    }
    
    # Count by type
    for rec in all_recommendations:
        rec_type = rec.recommendation_type.value
        stats["by_type"][rec_type] = stats["by_type"].get(rec_type, 0) + 1
    
    # Count by priority
    for rec in all_recommendations:
        priority = rec.priority
        stats["by_priority"][priority] = stats["by_priority"].get(priority, 0) + 1
    
    # Calculate average rating
    rated_recommendations = [r for r in all_recommendations if r.user_rating is not None]
    if rated_recommendations:
        stats["average_rating"] = sum(r.user_rating for r in rated_recommendations) / len(rated_recommendations)
    
    return stats


@router.get("/urgent/list")
def get_urgent_recommendations(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get urgent recommendations that need immediate attention"""
    recommendation_service = RecommendationService(db)
    
    all_recommendations = recommendation_service.get_user_recommendations(current_user.id, 0, 1000)
    
    # Filter urgent recommendations
    urgent_recommendations = [
        r for r in all_recommendations 
        if r.priority == "urgent" and r.status == "pending"
    ]
    
    # Sort by urgency_days (ascending)
    urgent_recommendations.sort(key=lambda x: x.urgency_days or 999)
    
    return {
        "urgent_recommendations": urgent_recommendations,
        "total_urgent": len(urgent_recommendations),
        "message": f"You have {len(urgent_recommendations)} urgent recommendations requiring immediate attention"
    }
