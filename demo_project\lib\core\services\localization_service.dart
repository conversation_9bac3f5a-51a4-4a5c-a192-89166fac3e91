import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../storage/storage_service.dart';

/// Supported languages in the app
enum SupportedLanguage {
  english('en', 'English', 'English'),
  tamil('ta', 'Tamil', 'தமிழ்'),
  malayalam('ml', 'Malayalam', 'മലയാളം');

  const SupportedLanguage(this.code, this.englishName, this.nativeName);

  final String code;
  final String englishName;
  final String nativeName;

  static SupportedLanguage fromCode(String code) {
    return SupportedLanguage.values.firstWhere(
      (lang) => lang.code == code,
      orElse: () => SupportedLanguage.english,
    );
  }
}

/// Localization state
class LocalizationState {
  final Locale locale;
  final SupportedLanguage language;
  final bool isLoading;

  const LocalizationState({
    required this.locale,
    required this.language,
    this.isLoading = false,
  });

  LocalizationState copyWith({
    Locale? locale,
    SupportedLanguage? language,
    bool? isLoading,
  }) {
    return LocalizationState(
      locale: locale ?? this.locale,
      language: language ?? this.language,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// Localization service for managing app language
class LocalizationService extends StateNotifier<LocalizationState> {
  final StorageService _storageService;

  LocalizationService(this._storageService)
    : super(
        LocalizationState(
          locale: const Locale('en'),
          language: SupportedLanguage.english,
        ),
      ) {
    // Load saved language asynchronously to avoid blocking initialization
    Future.microtask(() => _loadSavedLanguage());
  }

  /// Load saved language from storage
  Future<void> _loadSavedLanguage() async {
    try {
      // Add a small delay to ensure storage is ready
      await Future.delayed(const Duration(milliseconds: 500));

      final savedLanguageCode = _storageService.getLanguage();
      final language = SupportedLanguage.fromCode(savedLanguageCode);

      if (mounted) {
        state = state.copyWith(
          locale: Locale(language.code),
          language: language,
        );
      }
    } catch (e) {
      // If loading fails, keep default language
      // This is expected on first run or if storage isn't ready
      // Silent fail - this is expected on first run
    }
  }

  /// Change app language
  Future<void> changeLanguage(SupportedLanguage language) async {
    state = state.copyWith(isLoading: true);

    try {
      await _storageService.saveLanguage(language.code);

      state = state.copyWith(
        locale: Locale(language.code),
        language: language,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false);
      rethrow;
    }
  }

  /// Get current language
  SupportedLanguage get currentLanguage => state.language;

  /// Get current locale
  Locale get currentLocale => state.locale;

  /// Check if language is supported
  static bool isLanguageSupported(String languageCode) {
    return SupportedLanguage.values.any((lang) => lang.code == languageCode);
  }

  /// Get supported locales
  static List<Locale> get supportedLocales {
    return SupportedLanguage.values.map((lang) => Locale(lang.code)).toList();
  }

  /// Get language display name
  static String getLanguageDisplayName(
    String languageCode, {
    bool useNative = false,
  }) {
    final language = SupportedLanguage.fromCode(languageCode);
    return useNative ? language.nativeName : language.englishName;
  }
}

/// Localization provider
final localizationServiceProvider =
    StateNotifierProvider<LocalizationService, LocalizationState>((ref) {
      try {
        final storageService = ref.read(storageServiceProvider);
        return LocalizationService(storageService);
      } catch (e) {
        // If storage service fails, create with a new instance
        final fallbackStorage = StorageService();
        return LocalizationService(fallbackStorage);
      }
    });

/// Current locale provider
final currentLocaleProvider = Provider<Locale>((ref) {
  return ref.watch(localizationServiceProvider).locale;
});

/// Current language provider
final currentLanguageProvider = Provider<SupportedLanguage>((ref) {
  return ref.watch(localizationServiceProvider).language;
});

/// Storage service provider (if not already defined elsewhere)
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

/// Locale resolution delegate
class AppLocaleResolutionCallback {
  static Locale? localeResolutionCallback(
    Locale? locale,
    Iterable<Locale> supportedLocales,
  ) {
    if (locale == null) {
      return supportedLocales.first;
    }

    // Try to find exact match
    if (supportedLocales.contains(locale)) {
      return locale;
    }

    // Try to find language match
    for (final supportedLocale in supportedLocales) {
      if (locale.languageCode == supportedLocale.languageCode) {
        return supportedLocale;
      }
    }

    // Return default locale
    return supportedLocales.first;
  }
}

/// Extension for easy access to localizations
extension LocalizationExtension on BuildContext {
  /// Get app localizations
  // AppLocalizations get l10n => AppLocalizations.of(this)!;

  /// Get current locale
  Locale get locale => Localizations.localeOf(this);

  /// Check if current language is RTL
  bool get isRTL {
    final languageCode = locale.languageCode;
    return ['ar', 'he', 'fa', 'ur'].contains(languageCode);
  }
}
