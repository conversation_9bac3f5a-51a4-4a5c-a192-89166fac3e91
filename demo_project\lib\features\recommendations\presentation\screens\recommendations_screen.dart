import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/bottom_navigation.dart';

/// Recommendations screen
class RecommendationsScreen extends ConsumerWidget {
  const RecommendationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Recommendations'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.refresh),
            onPressed: () {
              // TODO: Generate new recommendations
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 6, // Mock data
        itemBuilder: (context, index) {
          final recommendations = [
            {
              'title': 'Nitrogen Fertilizer Application',
              'description': 'Apply 60 kg/ha of Urea in Field A',
              'priority': 'High',
              'color': AppTheme.errorRed,
              'icon': MdiIcons.testTube,
              'urgency': '3 days',
            },
            {
              'title': 'Irrigation Scheduling',
              'description': 'Irrigate Field B in next 2 days',
              'priority': 'Medium',
              'color': AppTheme.warningOrange,
              'icon': MdiIcons.water,
              'urgency': '2 days',
            },
            {
              'title': 'Pest Monitoring',
              'description': 'Check for aphids in Field C',
              'priority': 'Low',
              'color': AppTheme.successGreen,
              'icon': MdiIcons.bug,
              'urgency': '1 week',
            },
            {
              'title': 'Soil pH Adjustment',
              'description': 'Apply lime to Field D',
              'priority': 'Medium',
              'color': AppTheme.warningOrange,
              'icon': MdiIcons.testTube,
              'urgency': '1 week',
            },
            {
              'title': 'Harvest Planning',
              'description': 'Field A ready for harvest in 2 weeks',
              'priority': 'Low',
              'color': AppTheme.infoBlue,
              'icon': MdiIcons.leaf,
              'urgency': '2 weeks',
            },
            {
              'title': 'Weather Alert',
              'description': 'Heavy rain expected, postpone spraying',
              'priority': 'High',
              'color': AppTheme.errorRed,
              'icon': MdiIcons.weatherRainy,
              'urgency': 'Immediate',
            },
          ];

          final rec = recommendations[index];

          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: (rec['color'] as Color).withOpacity(0.1),
                child: Icon(
                  rec['icon'] as IconData,
                  color: rec['color'] as Color,
                ),
              ),
              title: Text(rec['title'] as String),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(rec['description'] as String),
                  const SizedBox(height: 4),
                  Text(
                    'Due in: ${rec['urgency']}',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textHint,
                    ),
                  ),
                ],
              ),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: (rec['color'] as Color).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  rec['priority'] as String,
                  style: AppTheme.labelSmall.copyWith(
                    color: rec['color'] as Color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              isThreeLine: true,
              onTap: () {
                _showRecommendationDetail(context, rec);
              },
            ),
          );
        },
      ),
      bottomNavigationBar: const AppBottomNavigation(currentIndex: 4),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Generate new recommendations
        },
        child: Icon(MdiIcons.autoFix),
      ),
    );
  }

  void _showRecommendationDetail(
    BuildContext context,
    Map<String, dynamic> rec,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(rec['icon'] as IconData, color: rec['color'] as Color),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    rec['title'] as String,
                    style: AppTheme.titleMedium,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: (rec['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    rec['priority'] as String,
                    style: AppTheme.labelSmall.copyWith(
                      color: rec['color'] as Color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(rec['description'] as String, style: AppTheme.bodyMedium),
            const SizedBox(height: 16),
            Text(
              'Due in: ${rec['urgency']}',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Dismiss'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // TODO: Mark as completed
                    },
                    child: const Text('Mark Done'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
