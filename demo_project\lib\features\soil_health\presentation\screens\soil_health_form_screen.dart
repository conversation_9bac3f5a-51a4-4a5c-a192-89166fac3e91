import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_button.dart';

/// Soil health card form screen
class SoilHealthFormScreen extends ConsumerStatefulWidget {
  final String? cardId;

  const SoilHealthFormScreen({super.key, this.cardId});

  @override
  ConsumerState<SoilHealthFormScreen> createState() =>
      _SoilHealthFormScreenState();
}

class _SoilHealthFormScreenState extends ConsumerState<SoilHealthFormScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  // Form controllers
  final _fieldNameController = TextEditingController();
  final _phController = TextEditingController();
  final _nitrogenController = TextEditingController();
  final _phosphorusController = TextEditingController();
  final _potassiumController = TextEditingController();
  final _cropTypeController = TextEditingController();
  final _lccController = TextEditingController();

  String _selectedGrowthStage = 'vegetative';
  String _analysisResult = '';
  bool _showResults = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.cardId == null
              ? 'Add Soil Health Card'
              : 'Edit Soil Health Card',
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Basic Information Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Basic Information',
                        style: AppTheme.titleMedium.copyWith(
                          color: AppTheme.primaryGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _fieldNameController,
                        label: 'Field Name',
                        prefixIcon: MdiIcons.barn,
                        validator: (value) =>
                            value?.isEmpty == true ? 'Required' : null,
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _cropTypeController,
                        label: 'Crop Type',
                        prefixIcon: MdiIcons.sprout,
                        validator: (value) =>
                            value?.isEmpty == true ? 'Required' : null,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Soil Analysis Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Soil Analysis',
                        style: AppTheme.titleMedium.copyWith(
                          color: AppTheme.primaryGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _phController,
                        label: 'pH Level (6.0-8.5)',
                        prefixIcon: MdiIcons.testTube,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty == true) return 'Required';
                          final ph = double.tryParse(value!);
                          if (ph == null || ph < 0 || ph > 14) {
                            return 'Enter valid pH (0-14)';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _nitrogenController,
                        label: 'Nitrogen (N) kg/ha',
                        prefixIcon: MdiIcons.leaf,
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _phosphorusController,
                        label: 'Phosphorus (P) kg/ha',
                        prefixIcon: MdiIcons.leaf,
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        controller: _potassiumController,
                        label: 'Potassium (K) kg/ha',
                        prefixIcon: MdiIcons.leaf,
                        keyboardType: TextInputType.number,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Analyze Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _analyzeData,
                  icon: Icon(MdiIcons.chartLine),
                  label: const Text('Analyze Soil Health'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen,
                    foregroundColor: AppTheme.textOnPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),

              // Analysis Results
              if (_showResults) ...[
                const SizedBox(height: 24),
                _buildAnalysisResults(),
              ],

              const SizedBox(height: 24),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: LoadingButton(
                  onPressed: _saveSoilHealthCard,
                  isLoading: _isLoading,
                  child: const Text('Save Soil Health Card'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fieldNameController.dispose();
    _phController.dispose();
    _nitrogenController.dispose();
    _phosphorusController.dispose();
    _potassiumController.dispose();
    _cropTypeController.dispose();
    _lccController.dispose();
    super.dispose();
  }

  void _analyzeData() {
    if (!_formKey.currentState!.validate()) return;

    final ph = double.tryParse(_phController.text) ?? 0.0;
    final nitrogen = double.tryParse(_nitrogenController.text) ?? 0.0;
    final phosphorus = double.tryParse(_phosphorusController.text) ?? 0.0;
    final potassium = double.tryParse(_potassiumController.text) ?? 0.0;

    // Simple analysis logic
    final analysis = _performSoilAnalysis(ph, nitrogen, phosphorus, potassium);

    setState(() {
      _analysisResult = analysis;
      _showResults = true;
    });
  }

  String _performSoilAnalysis(double ph, double n, double p, double k) {
    List<String> issues = [];
    List<String> recommendations = [];

    // pH Analysis
    if (ph < 6.0) {
      issues.add('Soil is too acidic (pH: ${ph.toStringAsFixed(1)})');
      recommendations.add('Apply lime to increase pH');
    } else if (ph > 8.5) {
      issues.add('Soil is too alkaline (pH: ${ph.toStringAsFixed(1)})');
      recommendations.add('Apply sulfur or organic matter to decrease pH');
    }

    // Nitrogen Analysis
    if (n < 200) {
      issues.add('Low nitrogen levels (${n.toStringAsFixed(0)} kg/ha)');
      recommendations.add(
        'Apply nitrogen-rich fertilizer (urea or ammonium sulfate)',
      );
    } else if (n > 400) {
      issues.add('High nitrogen levels (${n.toStringAsFixed(0)} kg/ha)');
      recommendations.add('Reduce nitrogen application to prevent leaching');
    }

    // Phosphorus Analysis
    if (p < 15) {
      issues.add('Low phosphorus levels (${p.toStringAsFixed(0)} kg/ha)');
      recommendations.add('Apply phosphate fertilizer (DAP or SSP)');
    }

    // Potassium Analysis
    if (k < 150) {
      issues.add('Low potassium levels (${k.toStringAsFixed(0)} kg/ha)');
      recommendations.add('Apply potash fertilizer (MOP or SOP)');
    }

    if (issues.isEmpty) {
      return 'Excellent soil health! All nutrient levels are optimal for crop growth.';
    }

    return 'Issues found:\n${issues.join('\n')}\n\nRecommendations:\n${recommendations.join('\n')}';
  }

  Widget _buildAnalysisResults() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.checkCircle,
                  color: AppTheme.successGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Analysis Results',
                  style: AppTheme.titleMedium.copyWith(
                    color: AppTheme.successGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                ),
              ),
              child: Text(_analysisResult, style: AppTheme.bodyMedium),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveSoilHealthCard() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.cardId == null
                  ? 'Soil health card created successfully!'
                  : 'Soil health card updated successfully!',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving soil health card: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
