import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';

/// Leaf analysis detail screen
class LeafAnalysisDetailScreen extends ConsumerWidget {
  final int analysisId;
  
  const LeafAnalysisDetailScreen({super.key, required this.analysisId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Analysis #$analysisId'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.share),
            onPressed: () {
              // TODO: Share analysis
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Image and Basic Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Container(
                      height: 200,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        MdiIcons.leaf,
                        size: 64,
                        color: AppTheme.accentGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(child: Text('Crop: Rice')),
                        Expanded(child: Text('Field: A')),
                        Expanded(child: Text('LCC: 3.5')),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Analysis Results
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('AI Analysis Results', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    _buildResultItem('Nitrogen Status', 'Moderate Deficiency', AppTheme.warningOrange),
                    _buildResultItem('Phosphorus Status', 'Normal', AppTheme.successGreen),
                    _buildResultItem('Potassium Status', 'Normal', AppTheme.successGreen),
                    _buildResultItem('Overall Health', 'Good', AppTheme.successGreen),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Recommendations
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Recommendations', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    _buildRecommendationItem(
                      'Apply Nitrogen Fertilizer',
                      'Apply 60 kg/ha of Urea within 3-5 days',
                      AppTheme.warningOrange,
                    ),
                    _buildRecommendationItem(
                      'Monitor Growth',
                      'Check leaf color again in 2 weeks',
                      AppTheme.infoBlue,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(child: Text(label)),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: AppTheme.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(String title, String description, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(MdiIcons.lightbulbOn, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600)),
                Text(description, style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
