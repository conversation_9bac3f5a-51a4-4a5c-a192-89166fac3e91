"""
User model for authentication and profile management
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class User(Base):
    """User model for farmers and agricultural advisors"""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    full_name = Column(String(255), nullable=False)
    phone_number = Column(String(20))
    
    # Farm information
    farm_name = Column(String(255))
    farm_location = Column(String(255))
    farm_size_acres = Column(Float)
    latitude = Column(Float)
    longitude = Column(Float)
    
    # User preferences
    preferred_language = Column(String(10), default="en")
    notification_preferences = Column(Text)  # JSON string
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    user_type = Column(String(20), default="farmer")  # farmer, advisor, admin
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    soil_health_cards = relationship("SoilHealthCard", back_populates="user")
    leaf_analyses = relationship("LeafAnalysis", back_populates="user")
    recommendations = relationship("Recommendation", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
