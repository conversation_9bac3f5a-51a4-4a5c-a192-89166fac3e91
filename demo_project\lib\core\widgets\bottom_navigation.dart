import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../router/app_router.dart';

/// Bottom navigation bar for main app navigation
class AppBottomNavigation extends StatelessWidget {
  final int currentIndex;

  const AppBottomNavigation({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: currentIndex,
      type: BottomNavigationBarType.fixed,
      onTap: (index) => _onItemTapped(context, index),
      items: [
        BottomNavigationBarItem(
          icon: Icon(MdiIcons.viewDashboard),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: Icon(MdiIcons.testTube),
          label: 'Soil Health',
        ),
        BottomNavigationBarItem(
          icon: Icon(MdiIcons.weatherSunny),
          label: 'Weather',
        ),
        BottomNavigationBarItem(
          icon: Icon(MdiIcons.leaf),
          label: 'Leaf Analysis',
        ),
        BottomNavigationBarItem(
          icon: Icon(MdiIcons.lightbulbOn),
          label: 'Recommendations',
        ),
      ],
    );
  }

  void _onItemTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go(AppRouter.dashboard);
        break;
      case 1:
        context.go(AppRouter.soilHealth);
        break;
      case 2:
        context.go(AppRouter.weather);
        break;
      case 3:
        context.go(AppRouter.leafAnalysis);
        break;
      case 4:
        context.go(AppRouter.recommendations);
        break;
    }
  }
}
