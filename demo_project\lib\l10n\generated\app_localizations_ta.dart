// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Tamil (`ta`).
class AppLocalizationsTa extends AppLocalizations {
  AppLocalizationsTa([String locale = 'ta']) : super(locale);

  @override
  String get appName => 'ஃபார்ம்ஸ்மார்ட் AI';

  @override
  String get welcome => 'வரவேற்கிறோம்';

  @override
  String welcomeBack(String name) {
    return 'மீண்டும் வரவேற்கிறோம், $name!';
  }

  @override
  String get login => 'உள்நுழைவு';

  @override
  String get register => 'பதிவு';

  @override
  String get logout => 'வெளியேறு';

  @override
  String get email => 'மின்னஞ்சல்';

  @override
  String get password => 'கடவுச்சொல்';

  @override
  String get confirmPassword => 'கடவுச்சொல்லை உறுதிப்படுத்தவும்';

  @override
  String get username => 'பயனர் பெயர்';

  @override
  String get fullName => 'முழு பெயர்';

  @override
  String get phoneNumber => 'தொலைபேசி எண்';

  @override
  String get farmName => 'பண்ணையின் பெயர்';

  @override
  String get farmLocation => 'பண்ணையின் இடம்';

  @override
  String get farmSize => 'பண்ணையின் அளவு';

  @override
  String get acres => 'ஏக்கர்';

  @override
  String get signIn => 'உள்நுழைக';

  @override
  String get signUp => 'பதிவு செய்க';

  @override
  String get forgotPassword => 'கடவுச்சொல் மறந்துவிட்டதா?';

  @override
  String get rememberMe => 'என்னை நினைவில் வைத்துக்கொள்';

  @override
  String get createAccount => 'கணக்கை உருவாக்கவும்';

  @override
  String get alreadyHaveAccount => 'ஏற்கனவே கணக்கு உள்ளதா?';

  @override
  String get dontHaveAccount => 'கணக்கு இல்லையா?';

  @override
  String get dashboard => 'டாஷ்போர்டு';

  @override
  String get profile => 'சுயவிவரம்';

  @override
  String get settings => 'அமைப்புகள்';

  @override
  String get soilHealth => 'மண் ஆரோக்கியம்';

  @override
  String get weather => 'வானிலை';

  @override
  String get leafAnalysis => 'இலை பகுப்பாய்வு';

  @override
  String get recommendations => 'பரிந்துரைகள்';

  @override
  String get crops => 'பயிர்கள்';

  @override
  String get quickOverview => 'விரைவு கண்ணோட்டம்';

  @override
  String get quickActions => 'விரைவு செயல்கள்';

  @override
  String get weatherSummary => 'வானிலை சுருக்கம்';

  @override
  String get recentActivities => 'சமீபத்திய செயல்பாடுகள்';

  @override
  String get activeRecommendations => 'செயலில் உள்ள பரிந்துரைகள்';

  @override
  String get soilHealthCards => 'மண் ஆரோக்கிய அட்டைகள்';

  @override
  String get temperature => 'வெப்பநிலை';

  @override
  String get humidity => 'ஈரப்பதம்';

  @override
  String get rainfall => 'மழைப்பொழிவு';

  @override
  String get wind => 'காற்று';

  @override
  String get forecast => '7-நாள் முன்னறிவிப்பு';

  @override
  String get today => 'இன்று';

  @override
  String get tomorrow => 'நாளை';

  @override
  String get notifications => 'அறிவிப்புகள்';

  @override
  String get markAllAsRead => 'அனைத்தையும் படித்ததாக குறிக்கவும்';

  @override
  String get weatherAlert => 'வானிலை எச்சரிக்கை';

  @override
  String get fertilizerRecommendation => 'உர பரிந்துரை';

  @override
  String get analysisComplete => 'பகுப்பாய்வு முடிந்தது';

  @override
  String get soilTest => 'மண் பரிசோதனை';

  @override
  String get takePhoto => 'புகைப்படம் எடுக்கவும்';

  @override
  String get camera => 'கேமரா';

  @override
  String get gallery => 'கேலரி';

  @override
  String get addSoilCard => 'மண் அட்டை சேர்க்கவும்';

  @override
  String get editProfile => 'சுயவிவரத்தை திருத்தவும்';

  @override
  String get personalInformation => 'தனிப்பட்ட தகவல்';

  @override
  String get farmInformation => 'பண்ணை தகவல்';

  @override
  String get accountSettings => 'கணக்கு அமைப்புகள்';

  @override
  String get appSettings => 'பயன்பாட்டு அமைப்புகள்';

  @override
  String get dataStorage => 'தரவு மற்றும் சேமிப்பு';

  @override
  String get support => 'ஆதரவு';

  @override
  String get theme => 'தீம்';

  @override
  String get language => 'மொழி';

  @override
  String get changePassword => 'கடவுச்சொல்லை மாற்றவும்';

  @override
  String get syncData => 'தரவை ஒத்திசைக்கவும்';

  @override
  String get exportData => 'தரவை ஏற்றுமதி செய்யவும்';

  @override
  String get clearCache => 'கேச் அழிக்கவும்';

  @override
  String get helpFaq => 'உதவி மற்றும் FAQ';

  @override
  String get contactSupport => 'ஆதரவைத் தொடர்பு கொள்ளவும்';

  @override
  String get about => 'பற்றி';

  @override
  String get save => 'சேமிக்கவும்';

  @override
  String get cancel => 'ரத்து செய்யவும்';

  @override
  String get delete => 'நீக்கவும்';

  @override
  String get edit => 'திருத்தவும்';

  @override
  String get add => 'சேர்க்கவும்';

  @override
  String get update => 'புதுப்பிக்கவும்';

  @override
  String get refresh => 'புதுப்பிக்கவும்';

  @override
  String get loading => 'ஏற்றுகிறது...';

  @override
  String get error => 'பிழை';

  @override
  String get success => 'வெற்றி';

  @override
  String get warning => 'எச்சரிக்கை';

  @override
  String get info => 'தகவல்';

  @override
  String get required => 'தேவையான';

  @override
  String get optional => 'விருப்பமான';

  @override
  String get notSpecified => 'குறிப்பிடப்படவில்லை';

  @override
  String get fieldName => 'வயல் பெயர்';

  @override
  String get phLevel => 'pH அளவு';

  @override
  String get nitrogen => 'நைட்ரஜன் (N) kg/ha';

  @override
  String get phosphorus => 'பாஸ்பரஸ் (P) kg/ha';

  @override
  String get potassium => 'பொட்டாசியம் (K) kg/ha';

  @override
  String get cropType => 'பயிர் வகை';

  @override
  String get growthStage => 'வளர்ச்சி நிலை';

  @override
  String get lccReading => 'LCC அளவீடு (1-6)';

  @override
  String get analyze => 'பகுப்பாய்வு செய்யவும்';

  @override
  String get results => 'முடிவுகள்';

  @override
  String get nitrogenStatus => 'நைட்ரஜன் நிலை';

  @override
  String get phosphorusStatus => 'பாஸ்பரஸ் நிலை';

  @override
  String get potassiumStatus => 'பொட்டாசியம் நிலை';

  @override
  String get overallHealth => 'ஒட்டுமொத்த ஆரோக்கியம்';

  @override
  String get normal => 'சாதாரண';

  @override
  String get low => 'குறைவு';

  @override
  String get medium => 'நடுத்தர';

  @override
  String get high => 'அதிக';

  @override
  String get optimal => 'உகந்த';

  @override
  String get good => 'நல்ல';

  @override
  String get poor => 'மோசமான';

  @override
  String get excellent => 'சிறந்த';

  @override
  String get deficiency => 'குறைபாடு';

  @override
  String get mild => 'லேசான';

  @override
  String get moderate => 'மிதமான';

  @override
  String get severe => 'கடுமையான';

  @override
  String get applyFertilizer => 'உரம் பயன்படுத்தவும்';

  @override
  String get irrigation => 'நீர்ப்பாசனம்';

  @override
  String get pestControl => 'பூச்சி கட்டுப்பாடு';

  @override
  String get monitoring => 'கண்காணிப்பு';

  @override
  String get harvest => 'அறுவடை';

  @override
  String get priority => 'முன்னுரிமை';

  @override
  String get urgent => 'அவசர';

  @override
  String get immediate => 'உடனடி';

  @override
  String get days => 'நாட்கள்';

  @override
  String get weeks => 'வாரங்கள்';

  @override
  String get hours => 'மணிநேரங்கள்';

  @override
  String get minutes => 'நிமிடங்கள்';

  @override
  String get ago => 'முன்பு';

  @override
  String get next => 'அடுத்து';

  @override
  String get previous => 'முந்தைய';

  @override
  String get viewAll => 'அனைத்தையும் பார்க்கவும்';

  @override
  String get viewDetails => 'விவரங்களைப் பார்க்கவும்';

  @override
  String get markDone => 'முடிந்ததாக குறிக்கவும்';

  @override
  String get dismiss => 'நிராகரிக்கவும்';

  @override
  String get retry => 'மீண்டும் முயற்சிக்கவும்';

  @override
  String get continueButton => 'தொடரவும்';

  @override
  String get close => 'மூடவும்';

  @override
  String get ok => 'சரி';

  @override
  String get yes => 'ஆம்';

  @override
  String get no => 'இல்லை';

  @override
  String get light => 'வெளிச்சம்';

  @override
  String get dark => 'இருள்';

  @override
  String get system => 'கணினி';

  @override
  String get english => 'ஆங்கிலம்';

  @override
  String get tamil => 'தமிழ்';

  @override
  String get malayalam => 'மலையாளம்';

  @override
  String get farmer => 'விவசாயி';

  @override
  String get expert => 'நிபுணர்';

  @override
  String get advisor => 'ஆலோசகர்';

  @override
  String get student => 'மாணவர்';

  @override
  String get researcher => 'ஆராய்ச்சியாளர்';

  @override
  String get loginSuccessful => 'உள்நுழைவு வெற்றிகரமாக';

  @override
  String get loginFailed => 'உள்நுழைவு தோல்வி';

  @override
  String get registrationSuccessful => 'பதிவு வெற்றிகரமாக';

  @override
  String get registrationFailed => 'பதிவு தோல்வி';

  @override
  String get profileUpdated => 'சுயவிவரம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get passwordChanged => 'கடவுச்சொல் வெற்றிகரமாக மாற்றப்பட்டது';

  @override
  String get logoutConfirm => 'நீங்கள் நிச்சயமாக வெளியேற விரும்புகிறீர்களா?';

  @override
  String get deleteConfirm =>
      'இந்த உருப்படியை நிச்சயமாக நீக்க விரும்புகிறீர்களா?';

  @override
  String get clearCacheConfirm =>
      'இது அனைத்து கேச் தரவையும் அழிக்கும். தொடரவா?';

  @override
  String get networkError =>
      'நெட்வொர்க் பிழை. உங்கள் இணைப்பைச் சரிபார்க்கவும்.';

  @override
  String get invalidCredentials => 'தவறான பயனர்பெயர் அல்லது கடவுச்சொல்';

  @override
  String get passwordTooShort =>
      'கடவுச்சொல் குறைந்தது 6 எழுத்துகளாக இருக்க வேண்டும்';

  @override
  String get emailInvalid => 'சரியான மின்னஞ்சல் முகவரியை உள்ளிடவும்';

  @override
  String get fieldRequired => 'இந்த புலம் தேவையானது';

  @override
  String get farmHealthy =>
      'உங்கள் பண்ணை ஆரோக்கியமாக தெரிகிறது! கீழே உள்ள சமீபத்திய பரிந்துரைகளைப் பார்க்கவும்.';

  @override
  String get goodConditions => 'உர பயன்பாட்டிற்கு நல்ல நிலைமைகள்';

  @override
  String get rainExpected =>
      'அடுத்த 24 மணி நேரத்தில் மழை எதிர்பார்க்கப்படுகிறது';

  @override
  String get nitrogenDeficiency =>
      'வயல் A இல் நைட்ரஜன் குறைபாடு கண்டறியப்பட்டது';

  @override
  String get soilAnalysisReady => 'மண் ஆரோக்கிய அட்டை பகுப்பாய்வு தயார்';

  @override
  String get captureLeafImage => 'இலை படத்தை எடுக்கவும்';

  @override
  String get takePhotoInstruction =>
      'AI பகுப்பாய்விற்காக இலையின் தெளிவான புகைப்படம் எடுக்கவும்';

  @override
  String get precisionAgriculture =>
      'ஸ்மார்ட் விவசாயத்திற்கான துல்லியமான விவசாயம்';

  @override
  String get initializing => 'துவக்குகிறது...';

  @override
  String get initializationError =>
      'பயன்பாட்டைத் துவக்க முடியவில்லை. பயன்பாட்டை மீண்டும் தொடங்கவும்.';

  @override
  String get basicInformation => 'அடிப்படை தகவல்';

  @override
  String get startWithDetails => 'உங்கள் அடிப்படை விவரங்களுடன் தொடங்குவோம்';

  @override
  String get acceptTerms =>
      'நான் விதிமுறைகள் மற்றும் நிபந்தனைகளை ஏற்றுக்கொள்கிறேன்';

  @override
  String get readTerms => 'விதிமுறைகள் மற்றும் நிபந்தனைகளைப் படிக்கவும்';

  @override
  String get createAccountButton => 'கணக்கை உருவாக்கவும்';

  @override
  String get back => 'பின்';

  @override
  String get emailVerification =>
      'சரிபார்ப்பு வழிமுறைகளுக்கு உங்கள் மின்னஞ்சலைச் சரிபார்க்கவும்.';

  @override
  String get continueToLogin => 'உள்நுழைவுக்குத் தொடரவும்';

  @override
  String get passwordResetSent =>
      'கடவுச்சொல் மீட்டமைப்பு இணைப்பு உங்கள் மின்னஞ்சலுக்கு அனுப்பப்பட்டது';

  @override
  String get enterEmailForReset =>
      'உங்கள் மின்னஞ்சல் முகவரியை உள்ளிடவும், கடவுச்சொல்லை மீட்டமைக்க இணைப்பை அனுப்புவோம்.';

  @override
  String get sendLink => 'இணைப்பை அனுப்பவும்';

  @override
  String get resetPassword => 'கடவுச்சொல்லை மீட்டமைக்கவும்';

  @override
  String get currentPassword => 'தற்போதைய கடவுச்சொல்';

  @override
  String get newPassword => 'புதிய கடவுச்சொல்';

  @override
  String get changePasswordSuccess => 'கடவுச்சொல் வெற்றிகரமாக மாற்றப்பட்டது';

  @override
  String get changePasswordFailed => 'கடவுச்சொல் மாற்ற முடியவில்லை';

  @override
  String get profilePicture => 'சுயவிவர படம்';

  @override
  String get changeProfilePicture => 'சுயவிவர படத்தை மாற்றவும்';

  @override
  String get saveChanges => 'மாற்றங்களைச் சேமிக்கவும்';

  @override
  String get discardChanges => 'மாற்றங்களை நிராகரிக்கவும்';

  @override
  String get unsavedChanges =>
      'உங்களிடம் சேமிக்கப்படாத மாற்றங்கள் உள்ளன. அவற்றைச் சேமிக்க விரும்புகிறீர்களா?';

  @override
  String get statistics => 'புள்ளிவிவரங்கள்';

  @override
  String get soilTests => 'மண் பரிசோதனைகள்';

  @override
  String get leafAnalyses => 'இலை பகுப்பாய்வுகள்';

  @override
  String get totalRecommendations => 'பரிந்துரைகள்';

  @override
  String get appVersion => 'பயன்பாட்டு பதிப்பு';

  @override
  String get buildNumber => 'பில்ட் எண்';

  @override
  String get developer => 'டெவலப்பர்';

  @override
  String get copyright =>
      '© 2024 ஃபார்ம்ஸ்மார்ட் AI. அனைத்து உரிமைகளும் பாதுகாக்கப்பட்டவை.';

  @override
  String get appDescription =>
      'மண் ஆரோக்கியம் மற்றும் பயிர் மேம்பாட்டிற்கான துல்லியமான விவசாய பயன்பாடு';

  @override
  String get appLongDescription =>
      'நிலையான விவசாய பரிந்துரைகளுக்காக மண் ஆரோக்கிய அட்டை, வானிலை தரவு மற்றும் இலை வண்ண அட்டவணையை ஒருங்கிணைக்கிறது.';

  @override
  String get cacheCleared => 'கேச் வெற்றிகரமாக அழிக்கப்பட்டது';

  @override
  String get dataSynced => 'தரவு வெற்றிகரமாக ஒத்திசைக்கப்பட்டது';

  @override
  String get dataExported => 'தரவு வெற்றிகரமாக ஏற்றுமதி செய்யப்பட்டது';

  @override
  String get notificationsEnabled => 'அறிவிப்புகள் இயக்கப்பட்டன';

  @override
  String get notificationsDisabled => 'அறிவிப்புகள் முடக்கப்பட்டன';

  @override
  String get themeChanged => 'தீம் வெற்றிகரமாக மாற்றப்பட்டது';

  @override
  String get languageChanged => 'மொழி வெற்றிகரமாக மாற்றப்பட்டது';

  @override
  String get restartRequired =>
      'மொழி மாற்றங்களைப் பயன்படுத்த பயன்பாட்டை மீண்டும் தொடங்கவும்';

  @override
  String get offlineMode => 'ஆஃப்லைன் பயன்முறை';

  @override
  String get onlineMode => 'ஆன்லைன் பயன்முறை';

  @override
  String get syncPending => 'ஒத்திசைவு நிலுவையில்';

  @override
  String get lastSynced => 'கடைசியாக ஒத்திசைக்கப்பட்டது';

  @override
  String get never => 'ஒருபோதும்';

  @override
  String get justNow => 'இப்போதே';
}
