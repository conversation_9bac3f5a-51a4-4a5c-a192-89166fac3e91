"""
Test configuration and fixtures
"""
import pytest
import tempfile
import os
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.database import get_db, Base
from app.models.user import User
from app.core.security import get_password_hash


# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine"""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create test database session"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client():
    """Create test client"""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def test_user(db_session):
    """Create test user"""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "hashed_password": get_password_hash("testpass123"),
        "full_name": "Test User",
        "user_type": "farmer",
        "is_active": True,
        "is_verified": True
    }
    
    user = User(**user_data)
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    
    return user


@pytest.fixture
def admin_user(db_session):
    """Create admin user"""
    user_data = {
        "email": "<EMAIL>",
        "username": "admin",
        "hashed_password": get_password_hash("admin123"),
        "full_name": "Admin User",
        "user_type": "admin",
        "is_active": True,
        "is_verified": True
    }
    
    user = User(**user_data)
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    
    return user


@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for test user"""
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    response = client.post("/api/v1/auth/login-json", json=login_data)
    token = response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers(client, admin_user):
    """Get authentication headers for admin user"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = client.post("/api/v1/auth/login-json", json=login_data)
    token = response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def temp_image_file():
    """Create temporary image file for testing"""
    # Create a simple test image (1x1 pixel PNG)
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        temp_file.write(png_data)
        temp_file.flush()
        yield temp_file.name
    
    # Clean up
    os.unlink(temp_file.name)


@pytest.fixture
def sample_soil_health_data():
    """Sample soil health card data"""
    return {
        "card_number": "SHC001",
        "field_name": "Test Field",
        "village": "Test Village",
        "district": "Test District",
        "state": "Test State",
        "soil_type": "Loamy",
        "ph_level": 6.5,
        "organic_carbon": 0.8,
        "nitrogen_n": 300,
        "phosphorus_p": 25,
        "potassium_k": 150,
        "zinc_zn": 0.8,
        "iron_fe": 5.0,
        "boron_b": 0.6
    }


@pytest.fixture
def sample_leaf_analysis_data():
    """Sample leaf analysis data"""
    return {
        "crop_type": "Rice",
        "crop_variety": "IR64",
        "growth_stage": "tillering",
        "field_name": "Test Field",
        "lcc_reading": 3.5,
        "lcc_category": "medium",
        "leaf_color_description": "Light green",
        "nitrogen_deficiency": True,
        "deficiency_severity": "moderate",
        "analysis_method": "image_ai"
    }
