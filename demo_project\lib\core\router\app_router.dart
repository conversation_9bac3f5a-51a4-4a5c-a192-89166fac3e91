import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import screens (will be created later)
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/register_screen.dart';
import '../../features/auth/presentation/screens/splash_screen.dart';
import '../../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../../features/profile/presentation/screens/profile_screen.dart';
import '../../features/soil_health/presentation/screens/soil_health_list_screen.dart';
import '../../features/soil_health/presentation/screens/soil_health_form_screen.dart';
import '../../features/soil_health/presentation/screens/soil_health_detail_screen.dart';
import '../../features/weather/presentation/screens/weather_screen.dart';
import '../../features/leaf_analysis/presentation/screens/leaf_analysis_list_screen.dart';
import '../../features/leaf_analysis/presentation/screens/leaf_analysis_form_screen.dart';
import '../../features/leaf_analysis/presentation/screens/leaf_analysis_detail_screen.dart';
import '../../features/recommendations/presentation/screens/recommendations_screen.dart';
import '../../features/crops/presentation/screens/crops_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';

/// Application routing configuration
class AppRouter {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String soilHealth = '/soil-health';
  static const String soilHealthForm = '/soil-health/form';
  static const String soilHealthDetail = '/soil-health/detail';
  static const String weather = '/weather';
  static const String leafAnalysis = '/leaf-analysis';
  static const String leafAnalysisForm = '/leaf-analysis/form';
  static const String leafAnalysisDetail = '/leaf-analysis/detail';
  static const String recommendations = '/recommendations';
  static const String crops = '/crops';
  static const String settings = '/settings';

  static GoRouter createRouter(WidgetRef ref) {
    return GoRouter(
      initialLocation: login,
      debugLogDiagnostics: true,
      routes: [
        // Splash Screen
        GoRoute(
          path: splash,
          name: 'splash',
          builder: (context, state) => const SplashScreen(),
        ),

        // Authentication Routes
        GoRoute(
          path: login,
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: register,
          name: 'register',
          builder: (context, state) => const RegisterScreen(),
        ),

        // Main App Routes
        GoRoute(
          path: dashboard,
          name: 'dashboard',
          builder: (context, state) => const DashboardScreen(),
        ),
        GoRoute(
          path: profile,
          name: 'profile',
          builder: (context, state) => const ProfileScreen(),
        ),

        // Soil Health Routes
        GoRoute(
          path: soilHealth,
          name: 'soil-health',
          builder: (context, state) => const SoilHealthListScreen(),
        ),
        GoRoute(
          path: soilHealthForm,
          name: 'soil-health-form',
          builder: (context, state) {
            final cardId = state.uri.queryParameters['cardId'];
            return SoilHealthFormScreen(cardId: cardId);
          },
        ),
        GoRoute(
          path: '$soilHealthDetail/:id',
          name: 'soil-health-detail',
          builder: (context, state) {
            final id = int.parse(state.pathParameters['id']!);
            return SoilHealthDetailScreen(cardId: id);
          },
        ),

        // Weather Routes
        GoRoute(
          path: weather,
          name: 'weather',
          builder: (context, state) => const WeatherScreen(),
        ),

        // Leaf Analysis Routes
        GoRoute(
          path: leafAnalysis,
          name: 'leaf-analysis',
          builder: (context, state) => const LeafAnalysisListScreen(),
        ),
        GoRoute(
          path: leafAnalysisForm,
          name: 'leaf-analysis-form',
          builder: (context, state) {
            final analysisId = state.uri.queryParameters['analysisId'];
            return LeafAnalysisFormScreen(analysisId: analysisId);
          },
        ),
        GoRoute(
          path: '$leafAnalysisDetail/:id',
          name: 'leaf-analysis-detail',
          builder: (context, state) {
            final id = int.parse(state.pathParameters['id']!);
            return LeafAnalysisDetailScreen(analysisId: id);
          },
        ),

        // Recommendations Routes
        GoRoute(
          path: recommendations,
          name: 'recommendations',
          builder: (context, state) => const RecommendationsScreen(),
        ),

        // Crops Routes
        GoRoute(
          path: crops,
          name: 'crops',
          builder: (context, state) => const CropsScreen(),
        ),

        // Settings Routes
        GoRoute(
          path: settings,
          name: 'settings',
          builder: (context, state) => const SettingsScreen(),
        ),
      ],

      // Error handling
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Page not found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                state.error?.toString() ?? 'Unknown error',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go(dashboard),
                child: const Text('Go to Dashboard'),
              ),
            ],
          ),
        ),
      ),

      // Redirect logic (will be implemented with auth state)
      redirect: (context, state) {
        // TODO: Implement authentication-based redirects
        return null;
      },
    );
  }
}
