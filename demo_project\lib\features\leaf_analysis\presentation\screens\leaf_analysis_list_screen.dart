import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/widgets/bottom_navigation.dart';

/// Leaf analysis list screen
class LeafAnalysisListScreen extends ConsumerWidget {
  const LeafAnalysisListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Leaf Analysis'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.plus),
            onPressed: () => context.go(AppRouter.leafAnalysisForm),
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 5, // Mock data
        itemBuilder: (context, index) {
          final deficiencies = ['None', 'Mild N', 'Moderate N', 'Severe N', 'Mild P'];
          final colors = [AppTheme.successGreen, AppTheme.warningOrange, AppTheme.warningOrange, AppTheme.errorRed, AppTheme.warningOrange];
          
          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: AppTheme.accentGreen.withOpacity(0.1),
                child: Icon(
                  MdiIcons.leaf,
                  color: AppTheme.accentGreen,
                ),
              ),
              title: Text('Field ${String.fromCharCode(65 + index)} - Rice'),
              subtitle: Text('LCC: ${3.5 + index * 0.2} • ${deficiencies[index]} deficiency'),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: colors[index].withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  deficiencies[index],
                  style: AppTheme.labelSmall.copyWith(color: colors[index]),
                ),
              ),
              onTap: () => context.go('${AppRouter.leafAnalysisDetail}/${index + 1}'),
            ),
          );
        },
      ),
      bottomNavigationBar: const AppBottomNavigation(currentIndex: 3),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRouter.leafAnalysisForm),
        child: Icon(MdiIcons.camera),
      ),
    );
  }
}
