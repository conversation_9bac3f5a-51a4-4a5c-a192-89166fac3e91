// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'FarmSmart AI';

  @override
  String get welcome => 'Welcome';

  @override
  String welcomeBack(String name) {
    return 'Welcome back, $name!';
  }

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get logout => 'Logout';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get username => 'Username';

  @override
  String get fullName => 'Full Name';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get farmName => 'Farm Name';

  @override
  String get farmLocation => 'Farm Location';

  @override
  String get farmSize => 'Farm Size';

  @override
  String get acres => 'acres';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get rememberMe => 'Remember me';

  @override
  String get createAccount => 'Create Account';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get soilHealth => 'Soil Health';

  @override
  String get weather => 'Weather';

  @override
  String get leafAnalysis => 'Leaf Analysis';

  @override
  String get recommendations => 'Recommendations';

  @override
  String get crops => 'Crops';

  @override
  String get quickOverview => 'Quick Overview';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get weatherSummary => 'Weather Summary';

  @override
  String get recentActivities => 'Recent Activities';

  @override
  String get activeRecommendations => 'Active Recommendations';

  @override
  String get soilHealthCards => 'Soil Health Cards';

  @override
  String get temperature => 'Temperature';

  @override
  String get humidity => 'Humidity';

  @override
  String get rainfall => 'Rainfall';

  @override
  String get wind => 'Wind';

  @override
  String get forecast => '7-Day Forecast';

  @override
  String get today => 'Today';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get notifications => 'Notifications';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get weatherAlert => 'Weather Alert';

  @override
  String get fertilizerRecommendation => 'Fertilizer Recommendation';

  @override
  String get analysisComplete => 'Analysis Complete';

  @override
  String get soilTest => 'Soil Test';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get addSoilCard => 'Add Soil Card';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get farmInformation => 'Farm Information';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get appSettings => 'App Settings';

  @override
  String get dataStorage => 'Data & Storage';

  @override
  String get support => 'Support';

  @override
  String get theme => 'Theme';

  @override
  String get language => 'Language';

  @override
  String get changePassword => 'Change Password';

  @override
  String get syncData => 'Sync Data';

  @override
  String get exportData => 'Export Data';

  @override
  String get clearCache => 'Clear Cache';

  @override
  String get helpFaq => 'Help & FAQ';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get about => 'About';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get update => 'Update';

  @override
  String get refresh => 'Refresh';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get required => 'Required';

  @override
  String get optional => 'Optional';

  @override
  String get notSpecified => 'Not specified';

  @override
  String get fieldName => 'Field Name';

  @override
  String get phLevel => 'pH Level';

  @override
  String get nitrogen => 'Nitrogen (N) kg/ha';

  @override
  String get phosphorus => 'Phosphorus (P) kg/ha';

  @override
  String get potassium => 'Potassium (K) kg/ha';

  @override
  String get cropType => 'Crop Type';

  @override
  String get growthStage => 'Growth Stage';

  @override
  String get lccReading => 'LCC Reading (1-6)';

  @override
  String get analyze => 'Analyze';

  @override
  String get results => 'Results';

  @override
  String get nitrogenStatus => 'Nitrogen Status';

  @override
  String get phosphorusStatus => 'Phosphorus Status';

  @override
  String get potassiumStatus => 'Potassium Status';

  @override
  String get overallHealth => 'Overall Health';

  @override
  String get normal => 'Normal';

  @override
  String get low => 'Low';

  @override
  String get medium => 'Medium';

  @override
  String get high => 'High';

  @override
  String get optimal => 'Optimal';

  @override
  String get good => 'Good';

  @override
  String get poor => 'Poor';

  @override
  String get excellent => 'Excellent';

  @override
  String get deficiency => 'Deficiency';

  @override
  String get mild => 'Mild';

  @override
  String get moderate => 'Moderate';

  @override
  String get severe => 'Severe';

  @override
  String get applyFertilizer => 'Apply Fertilizer';

  @override
  String get irrigation => 'Irrigation';

  @override
  String get pestControl => 'Pest Control';

  @override
  String get monitoring => 'Monitoring';

  @override
  String get harvest => 'Harvest';

  @override
  String get priority => 'Priority';

  @override
  String get urgent => 'Urgent';

  @override
  String get immediate => 'Immediate';

  @override
  String get days => 'days';

  @override
  String get weeks => 'weeks';

  @override
  String get hours => 'hours';

  @override
  String get minutes => 'minutes';

  @override
  String get ago => 'ago';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get viewAll => 'View All';

  @override
  String get viewDetails => 'View Details';

  @override
  String get markDone => 'Mark Done';

  @override
  String get dismiss => 'Dismiss';

  @override
  String get retry => 'Retry';

  @override
  String get continueButton => 'Continue';

  @override
  String get close => 'Close';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get light => 'Light';

  @override
  String get dark => 'Dark';

  @override
  String get system => 'System';

  @override
  String get english => 'English';

  @override
  String get tamil => 'Tamil';

  @override
  String get malayalam => 'Malayalam';

  @override
  String get farmer => 'Farmer';

  @override
  String get expert => 'Expert';

  @override
  String get advisor => 'Advisor';

  @override
  String get student => 'Student';

  @override
  String get researcher => 'Researcher';

  @override
  String get loginSuccessful => 'Login successful';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get registrationSuccessful => 'Registration successful';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get passwordChanged => 'Password changed successfully';

  @override
  String get logoutConfirm => 'Are you sure you want to logout?';

  @override
  String get deleteConfirm => 'Are you sure you want to delete this item?';

  @override
  String get clearCacheConfirm => 'This will clear all cached data. Continue?';

  @override
  String get networkError => 'Network error. Please check your connection.';

  @override
  String get invalidCredentials => 'Invalid username or password';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get emailInvalid => 'Please enter a valid email address';

  @override
  String get fieldRequired => 'This field is required';

  @override
  String get farmHealthy =>
      'Your farm is looking healthy! Check out the latest recommendations below.';

  @override
  String get goodConditions => 'Good conditions for fertilizer application';

  @override
  String get rainExpected => 'Rain expected in next 24 hours';

  @override
  String get nitrogenDeficiency => 'Nitrogen deficiency detected in Field A';

  @override
  String get soilAnalysisReady => 'Soil health card analysis ready';

  @override
  String get captureLeafImage => 'Capture Leaf Image';

  @override
  String get takePhotoInstruction =>
      'Take a clear photo of the leaf for AI analysis';

  @override
  String get precisionAgriculture => 'Precision Agriculture for Smart Farming';

  @override
  String get initializing => 'Initializing...';

  @override
  String get initializationError =>
      'Failed to initialize the app. Please restart the application.';

  @override
  String get basicInformation => 'Basic Information';

  @override
  String get startWithDetails => 'Let\'s start with your basic details';

  @override
  String get acceptTerms => 'I accept the Terms and Conditions';

  @override
  String get readTerms => 'Read Terms and Conditions';

  @override
  String get createAccountButton => 'Create Account';

  @override
  String get back => 'Back';

  @override
  String get emailVerification =>
      'Please check your email for verification instructions.';

  @override
  String get continueToLogin => 'Continue to Login';

  @override
  String get passwordResetSent => 'Password reset link sent to your email';

  @override
  String get enterEmailForReset =>
      'Enter your email address and we\'ll send you a link to reset your password.';

  @override
  String get sendLink => 'Send Link';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get changePasswordSuccess => 'Password changed successfully';

  @override
  String get changePasswordFailed => 'Failed to change password';

  @override
  String get profilePicture => 'Profile Picture';

  @override
  String get changeProfilePicture => 'Change Profile Picture';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get unsavedChanges =>
      'You have unsaved changes. Do you want to save them?';

  @override
  String get statistics => 'Statistics';

  @override
  String get soilTests => 'Soil Tests';

  @override
  String get leafAnalyses => 'Leaf Analyses';

  @override
  String get totalRecommendations => 'Recommendations';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get developer => 'Developer';

  @override
  String get copyright => '© 2024 FarmSmart AI. All rights reserved.';

  @override
  String get appDescription =>
      'Precision Agriculture App for Soil Health & Crop Optimization';

  @override
  String get appLongDescription =>
      'Integrates Soil Health Card, weather data, and Leaf Color Chart for sustainable farming recommendations.';

  @override
  String get cacheCleared => 'Cache cleared successfully';

  @override
  String get dataSynced => 'Data synced successfully';

  @override
  String get dataExported => 'Data exported successfully';

  @override
  String get notificationsEnabled => 'Notifications enabled';

  @override
  String get notificationsDisabled => 'Notifications disabled';

  @override
  String get themeChanged => 'Theme changed successfully';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get restartRequired =>
      'Please restart the app to apply language changes';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get onlineMode => 'Online Mode';

  @override
  String get syncPending => 'Sync pending';

  @override
  String get lastSynced => 'Last synced';

  @override
  String get never => 'Never';

  @override
  String get justNow => 'Just now';
}
