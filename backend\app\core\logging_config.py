"""
Logging configuration for the application
"""
import logging
import logging.handlers
import os
from datetime import datetime
from app.config import settings


def setup_logging():
    """Setup logging configuration"""
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(settings.log_file)
    os.makedirs(log_dir, exist_ok=True)
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create detailed formatter for file logging
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, settings.log_level.upper()))
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Error file handler
    error_log_file = settings.log_file.replace('.log', '_error.log')
    error_handler = logging.handlers.RotatingFileHandler(
        filename=error_log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    # Application loggers
    app_logger = logging.getLogger("app")
    app_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name"""
    return logging.getLogger(f"app.{name}")


class RequestLogger:
    """Middleware for logging HTTP requests"""
    
    def __init__(self):
        self.logger = get_logger("requests")
    
    async def log_request(self, request, call_next):
        """Log incoming requests"""
        start_time = datetime.utcnow()
        
        # Log request
        self.logger.info(
            f"Request: {request.method} {request.url} - "
            f"Client: {request.client.host if request.client else 'unknown'}"
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Log response
        self.logger.info(
            f"Response: {response.status_code} - "
            f"Time: {process_time:.3f}s - "
            f"Path: {request.url.path}"
        )
        
        return response


class DatabaseLogger:
    """Logger for database operations"""
    
    def __init__(self):
        self.logger = get_logger("database")
    
    def log_query(self, operation: str, table: str, duration: float = None):
        """Log database queries"""
        message = f"DB {operation}: {table}"
        if duration:
            message += f" - Duration: {duration:.3f}s"
        
        self.logger.debug(message)
    
    def log_error(self, operation: str, table: str, error: str):
        """Log database errors"""
        self.logger.error(f"DB Error in {operation} on {table}: {error}")


class ServiceLogger:
    """Logger for service operations"""
    
    def __init__(self, service_name: str):
        self.logger = get_logger(f"services.{service_name}")
        self.service_name = service_name
    
    def log_operation(self, operation: str, details: str = None):
        """Log service operations"""
        message = f"{self.service_name}: {operation}"
        if details:
            message += f" - {details}"
        
        self.logger.info(message)
    
    def log_error(self, operation: str, error: str):
        """Log service errors"""
        self.logger.error(f"{self.service_name} Error in {operation}: {error}")
    
    def log_warning(self, operation: str, warning: str):
        """Log service warnings"""
        self.logger.warning(f"{self.service_name} Warning in {operation}: {warning}")


class SecurityLogger:
    """Logger for security events"""
    
    def __init__(self):
        self.logger = get_logger("security")
    
    def log_login_attempt(self, username: str, success: bool, ip_address: str = None):
        """Log login attempts"""
        status = "SUCCESS" if success else "FAILED"
        message = f"Login {status}: {username}"
        if ip_address:
            message += f" from {ip_address}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.warning(message)
    
    def log_unauthorized_access(self, path: str, ip_address: str = None):
        """Log unauthorized access attempts"""
        message = f"Unauthorized access attempt: {path}"
        if ip_address:
            message += f" from {ip_address}"
        
        self.logger.warning(message)
    
    def log_permission_denied(self, user: str, resource: str, action: str):
        """Log permission denied events"""
        self.logger.warning(f"Permission denied: {user} tried to {action} {resource}")


# Initialize logging when module is imported
setup_logging()
