import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

/// User model representing a user in the system
@JsonSerializable()
class User {
  final int id;
  final String email;
  final String username;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'full_name')
  final String fullName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number')
  final String? phoneNumber;
  @Json<PERSON><PERSON>(name: 'farm_name')
  final String? farmName;
  @JsonKey(name: 'farm_location')
  final String? farmLocation;
  @<PERSON>son<PERSON><PERSON>(name: 'farm_size_acres')
  final double? farmSizeAcres;
  final double? latitude;
  final double? longitude;
  @<PERSON>son<PERSON>ey(name: 'preferred_language')
  final String preferredLanguage;
  @<PERSON>son<PERSON>ey(name: 'user_type')
  final String userType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON>son<PERSON>ey(name: 'is_verified')
  final bool isVerified;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login')
  final DateTime? lastLogin;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_picture')
  final String? profilePicture;
  final String? bio;
  @<PERSON>son<PERSON><PERSON>(name: 'years_experience')
  final int? yearsExperience;
  final List<String>? specializations;
  @JsonKey(name: 'notifications_enabled')
  final bool notificationsEnabled;

  const User({
    required this.id,
    required this.email,
    required this.username,
    required this.fullName,
    this.phoneNumber,
    this.farmName,
    this.farmLocation,
    this.farmSizeAcres,
    this.latitude,
    this.longitude,
    this.preferredLanguage = 'en',
    this.userType = 'farmer',
    this.isActive = true,
    this.isVerified = false,
    required this.createdAt,
    this.updatedAt,
    this.lastLogin,
    this.profilePicture,
    this.bio,
    this.yearsExperience,
    this.specializations,
    this.notificationsEnabled = true,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? email,
    String? username,
    String? fullName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    double? farmSizeAcres,
    double? latitude,
    double? longitude,
    String? preferredLanguage,
    String? userType,
    bool? isActive,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    String? profilePicture,
    String? bio,
    int? yearsExperience,
    List<String>? specializations,
    bool? notificationsEnabled,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      farmName: farmName ?? this.farmName,
      farmLocation: farmLocation ?? this.farmLocation,
      farmSizeAcres: farmSizeAcres ?? this.farmSizeAcres,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      userType: userType ?? this.userType,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      profilePicture: profilePicture ?? this.profilePicture,
      bio: bio ?? this.bio,
      yearsExperience: yearsExperience ?? this.yearsExperience,
      specializations: specializations ?? this.specializations,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, fullName: $fullName)';
  }
}

/// User registration request model
@JsonSerializable()
class UserRegistration {
  final String email;
  final String username;
  final String password;
  @JsonKey(name: 'full_name')
  final String fullName;
  @JsonKey(name: 'phone_number')
  final String? phoneNumber;
  @JsonKey(name: 'farm_name')
  final String? farmName;
  @JsonKey(name: 'farm_location')
  final String? farmLocation;
  @JsonKey(name: 'farm_size_acres')
  final double? farmSizeAcres;
  final double? latitude;
  final double? longitude;
  @JsonKey(name: 'preferred_language')
  final String preferredLanguage;
  @JsonKey(name: 'user_type')
  final String userType;

  const UserRegistration({
    required this.email,
    required this.username,
    required this.password,
    required this.fullName,
    this.phoneNumber,
    this.farmName,
    this.farmLocation,
    this.farmSizeAcres,
    this.latitude,
    this.longitude,
    this.preferredLanguage = 'en',
    this.userType = 'farmer',
  });

  factory UserRegistration.fromJson(Map<String, dynamic> json) =>
      _$UserRegistrationFromJson(json);
  Map<String, dynamic> toJson() => _$UserRegistrationToJson(this);
}

/// User login request model
@JsonSerializable()
class UserLogin {
  final String username;
  final String password;

  const UserLogin({required this.username, required this.password});

  factory UserLogin.fromJson(Map<String, dynamic> json) =>
      _$UserLoginFromJson(json);
  Map<String, dynamic> toJson() => _$UserLoginToJson(this);
}

/// Authentication token response model
@JsonSerializable()
class AuthToken {
  @JsonKey(name: 'access_token')
  final String accessToken;
  @JsonKey(name: 'token_type')
  final String tokenType;
  @JsonKey(name: 'expires_in')
  final int expiresIn;

  const AuthToken({
    required this.accessToken,
    required this.tokenType,
    required this.expiresIn,
  });

  factory AuthToken.fromJson(Map<String, dynamic> json) =>
      _$AuthTokenFromJson(json);
  Map<String, dynamic> toJson() => _$AuthTokenToJson(this);
}

/// User update request model
@JsonSerializable()
class UserUpdate {
  @JsonKey(name: 'full_name')
  final String? fullName;
  @JsonKey(name: 'phone_number')
  final String? phoneNumber;
  @JsonKey(name: 'farm_name')
  final String? farmName;
  @JsonKey(name: 'farm_location')
  final String? farmLocation;
  @JsonKey(name: 'farm_size_acres')
  final double? farmSizeAcres;
  final double? latitude;
  final double? longitude;
  @JsonKey(name: 'preferred_language')
  final String? preferredLanguage;
  @JsonKey(name: 'profile_picture')
  final String? profilePicture;
  final String? bio;
  @JsonKey(name: 'years_experience')
  final int? yearsExperience;
  final List<String>? specializations;
  @JsonKey(name: 'notifications_enabled')
  final bool? notificationsEnabled;

  const UserUpdate({
    this.fullName,
    this.phoneNumber,
    this.farmName,
    this.farmLocation,
    this.farmSizeAcres,
    this.latitude,
    this.longitude,
    this.preferredLanguage,
    this.profilePicture,
    this.bio,
    this.yearsExperience,
    this.specializations,
    this.notificationsEnabled,
  });

  factory UserUpdate.fromJson(Map<String, dynamic> json) =>
      _$UserUpdateFromJson(json);
  Map<String, dynamic> toJson() => _$UserUpdateToJson(this);
}
