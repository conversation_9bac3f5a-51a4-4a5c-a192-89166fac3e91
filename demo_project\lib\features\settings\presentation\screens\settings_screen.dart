import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/services/localization_service.dart';
import '../../../../core/widgets/sync_status_widget.dart';
import '../../../auth/data/providers/auth_provider.dart';

/// Settings screen
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Account Section
          _buildSectionHeader('Account'),
          _buildSettingsTile(
            icon: MdiIcons.account,
            title: 'Profile Settings',
            subtitle: 'Edit your profile information',
            onTap: () {
              // TODO: Navigate to profile settings
            },
          ),
          _buildSettingsTile(
            icon: MdiIcons.lock,
            title: 'Change Password',
            subtitle: 'Update your password',
            onTap: () {
              // TODO: Navigate to change password
            },
          ),

          const SizedBox(height: 24),

          // App Settings Section
          _buildSectionHeader('App Settings'),
          _buildSettingsTile(
            icon: MdiIcons.palette,
            title: 'Theme',
            subtitle: 'Light, Dark, or System',
            trailing: DropdownButton<String>(
              value: 'System',
              underline: const SizedBox(),
              items: ['Light', 'Dark', 'System'].map((theme) {
                return DropdownMenuItem(value: theme, child: Text(theme));
              }).toList(),
              onChanged: (value) {
                // TODO: Change theme
              },
            ),
          ),
          _buildSettingsTile(
            icon: MdiIcons.translate,
            title: 'Language',
            subtitle: ref.watch(currentLanguageProvider).englishName,
            onTap: () {
              _showLanguageDialog(context, ref);
            },
          ),
          _buildSettingsTile(
            icon: MdiIcons.bell,
            title: 'Notifications',
            subtitle: 'Manage notification preferences',
            trailing: Switch(
              value: true,
              onChanged: (value) {
                // TODO: Toggle notifications
              },
            ),
          ),

          const SizedBox(height: 24),

          // Data Section
          _buildSectionHeader('Data & Storage'),

          // Sync Status Widget
          const SyncStatusWidget(),

          const SizedBox(height: 16),
          _buildSettingsTile(
            icon: MdiIcons.download,
            title: 'Export Data',
            subtitle: 'Download your data',
            onTap: () {
              // TODO: Export data
            },
          ),
          _buildSettingsTile(
            icon: MdiIcons.deleteEmpty,
            title: 'Clear Cache',
            subtitle: 'Free up storage space',
            onTap: () {
              _showClearCacheDialog(context);
            },
          ),

          const SizedBox(height: 24),

          // Support Section
          _buildSectionHeader('Support'),
          _buildSettingsTile(
            icon: MdiIcons.helpCircle,
            title: 'Help & FAQ',
            subtitle: 'Get help and find answers',
            onTap: () {
              // TODO: Show help
            },
          ),
          _buildSettingsTile(
            icon: MdiIcons.email,
            title: 'Contact Support',
            subtitle: 'Get in touch with our team',
            onTap: () {
              // TODO: Contact support
            },
          ),
          _buildSettingsTile(
            icon: MdiIcons.information,
            title: 'About',
            subtitle: 'App version and information',
            onTap: () {
              _showAboutDialog(context);
            },
          ),

          const SizedBox(height: 24),

          // Logout Button
          Card(
            child: ListTile(
              leading: Icon(MdiIcons.logout, color: AppTheme.errorRed),
              title: Text(
                'Logout',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.errorRed,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                _showLogoutDialog(context, ref);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: AppTheme.titleMedium.copyWith(
          color: AppTheme.primaryGreen,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: AppTheme.primaryGreen),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: trailing ?? Icon(MdiIcons.chevronRight),
        onTap: onTap,
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.deleteEmpty, color: AppTheme.warningOrange),
            const SizedBox(width: 8),
            const Text('Clear Cache'),
          ],
        ),
        content: const Text(
          'This will clear all cached data including images and temporary files. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Clear cache
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache cleared successfully'),
                  backgroundColor: AppTheme.successGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.warningOrange,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'FarmSmart AI',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        MdiIcons.sprout,
        size: 48,
        color: AppTheme.primaryGreen,
      ),
      children: [
        const Text(
          'Precision Agriculture App for Soil Health & Crop Optimization',
        ),
        const SizedBox(height: 16),
        const Text(
          'Integrates Soil Health Card, weather data, and Leaf Color Chart '
          'for sustainable farming recommendations.',
        ),
        const SizedBox(height: 16),
        const Text('© 2024 FarmSmart AI. All rights reserved.'),
      ],
    );
  }

  void _showLanguageDialog(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.read(currentLanguageProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.translate, color: AppTheme.primaryGreen),
            const SizedBox(width: 8),
            const Text('Select Language'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: SupportedLanguage.values.map((language) {
            return RadioListTile<SupportedLanguage>(
              title: Text(language.englishName),
              subtitle: Text(language.nativeName),
              value: language,
              groupValue: currentLanguage,
              onChanged: (value) async {
                if (value != null) {
                  Navigator.pop(context);
                  final localizationService = ref.read(
                    localizationServiceProvider.notifier,
                  );
                  await localizationService.changeLanguage(value);

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Language changed to ${value.englishName}',
                        ),
                        backgroundColor: AppTheme.successGreen,
                      ),
                    );
                  }
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.logout, color: AppTheme.errorRed),
            const SizedBox(width: 8),
            const Text('Logout'),
          ],
        ),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final authNotifier = ref.read(authProvider.notifier);
              await authNotifier.logout();
              if (context.mounted) {
                context.go(AppRouter.login);
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorRed),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
