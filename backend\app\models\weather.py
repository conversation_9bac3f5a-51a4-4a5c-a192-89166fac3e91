"""
Weather data model for storing weather information
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Text
from sqlalchemy.sql import func
from app.database import Base


class WeatherData(Base):
    """Weather data model for storing current and forecast weather information"""
    
    __tablename__ = "weather_data"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Location information
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    location_name = Column(String(255))
    
    # Weather timestamp
    weather_date = Column(DateTime(timezone=True), nullable=False)
    data_type = Column(String(20), default="current")  # current, forecast, historical
    
    # Temperature data (Celsius)
    temperature = Column(Float)  # Current temperature
    feels_like = Column(Float)  # Feels like temperature
    temp_min = Column(Float)  # Minimum temperature
    temp_max = Column(Float)  # Maximum temperature
    
    # Atmospheric conditions
    humidity = Column(Float)  # Humidity percentage
    pressure = Column(Float)  # Atmospheric pressure in hPa
    visibility = Column(Float)  # Visibility in km
    
    # Wind data
    wind_speed = Column(Float)  # Wind speed in m/s
    wind_direction = Column(Float)  # Wind direction in degrees
    wind_gust = Column(Float)  # Wind gust speed in m/s
    
    # Precipitation
    rainfall = Column(Float)  # Rainfall in mm
    rainfall_1h = Column(Float)  # Rainfall in last 1 hour
    rainfall_3h = Column(Float)  # Rainfall in last 3 hours
    snow = Column(Float)  # Snow in mm
    
    # Cloud and weather conditions
    cloud_cover = Column(Float)  # Cloud coverage percentage
    weather_main = Column(String(50))  # Main weather condition
    weather_description = Column(String(255))  # Detailed weather description
    weather_icon = Column(String(10))  # Weather icon code
    
    # UV and solar data
    uv_index = Column(Float)  # UV index
    solar_radiation = Column(Float)  # Solar radiation
    
    # Sunrise and sunset (for daily data)
    sunrise = Column(DateTime(timezone=True))
    sunset = Column(DateTime(timezone=True))
    
    # Additional agricultural data
    dew_point = Column(Float)  # Dew point temperature
    soil_temperature = Column(Float)  # Soil temperature if available
    evapotranspiration = Column(Float)  # ET0 reference evapotranspiration
    
    # Data source and quality
    data_source = Column(String(50), default="openweather")  # API source
    data_quality = Column(String(20), default="good")  # good, fair, poor
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<WeatherData(id={self.id}, location='{self.location_name}', date='{self.weather_date}')>"
