"""
Recommendation models for storing AI-generated farming recommendations
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, <PERSON>olean, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base
import enum


class RecommendationType(enum.Enum):
    """Types of recommendations"""
    FERTILIZER = "fertilizer"
    PESTICIDE = "pesticide"
    IRRIGATION = "irrigation"
    PLANTING = "planting"
    HARVESTING = "harvesting"
    SOIL_MANAGEMENT = "soil_management"
    CROP_SELECTION = "crop_selection"
    GENERAL = "general"


class Recommendation(Base):
    """Recommendation model for storing AI-generated farming recommendations"""
    
    __tablename__ = "recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Related data sources
    soil_health_card_id = Column(Integer, ForeignKey("soil_health_cards.id"))
    leaf_analysis_id = Column(Integer, ForeignKey("leaf_analyses.id"))
    weather_data_ids = Column(Text)  # JSON array of weather data IDs used
    
    # Recommendation details
    recommendation_type = Column(Enum(RecommendationType), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    
    # Crop information
    crop_type = Column(String(100))
    crop_variety = Column(String(100))
    growth_stage = Column(String(50))
    
    # Fertilizer recommendations
    nitrogen_amount = Column(Float)  # kg/ha
    phosphorus_amount = Column(Float)  # kg/ha
    potassium_amount = Column(Float)  # kg/ha
    fertilizer_type = Column(String(100))
    fertilizer_application_method = Column(String(100))
    
    # Pesticide recommendations
    pesticide_name = Column(String(255))
    pesticide_dosage = Column(Float)
    pesticide_unit = Column(String(50))  # ml/L, g/L, etc.
    target_pest_disease = Column(String(255))
    
    # Timing recommendations
    recommended_date = Column(DateTime(timezone=True))
    optimal_time_start = Column(DateTime(timezone=True))
    optimal_time_end = Column(DateTime(timezone=True))
    weather_conditions_required = Column(Text)  # JSON with weather requirements
    
    # Application details
    application_frequency = Column(String(100))
    application_interval_days = Column(Integer)
    total_applications = Column(Integer)
    
    # Dosage and area
    dosage_per_acre = Column(Float)
    dosage_per_hectare = Column(Float)
    area_coverage = Column(Float)  # acres or hectares
    
    # Cost estimation
    estimated_cost = Column(Float)
    cost_currency = Column(String(10), default="INR")
    cost_breakdown = Column(Text)  # JSON with cost details
    
    # Expected outcomes
    expected_yield_increase = Column(Float)  # percentage
    expected_quality_improvement = Column(Text)
    environmental_impact = Column(String(50))  # low, medium, high
    
    # AI model information
    model_version = Column(String(50))
    confidence_score = Column(Float)  # 0-1 scale
    data_sources_used = Column(Text)  # JSON array of data sources
    
    # Priority and urgency
    priority = Column(String(20), default="medium")  # low, medium, high, urgent
    urgency_days = Column(Integer)  # Days within which action should be taken
    
    # Status and feedback
    status = Column(String(20), default="pending")  # pending, accepted, rejected, completed
    user_feedback = Column(Text)
    user_rating = Column(Integer)  # 1-5 scale
    implementation_date = Column(DateTime(timezone=True))
    completion_date = Column(DateTime(timezone=True))
    
    # Follow-up
    follow_up_required = Column(Boolean, default=False)
    follow_up_date = Column(DateTime(timezone=True))
    follow_up_notes = Column(Text)
    
    # Additional information
    precautions = Column(Text)
    alternative_options = Column(Text)  # JSON with alternative recommendations
    references = Column(Text)  # JSON with scientific references
    
    # Localization
    language = Column(String(10), default="en")
    local_names = Column(Text)  # JSON with local names for products/practices
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True))  # When recommendation becomes outdated
    
    # Relationships
    user = relationship("User", back_populates="recommendations")
    soil_health_card = relationship("SoilHealthCard", back_populates="recommendations")
    leaf_analysis = relationship("LeafAnalysis", back_populates="recommendations")
    
    def __repr__(self):
        return f"<Recommendation(id={self.id}, type='{self.recommendation_type}', user_id={self.user_id})>"
