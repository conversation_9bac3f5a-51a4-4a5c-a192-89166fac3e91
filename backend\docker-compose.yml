version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: farmsmart_db
      POSTGRES_USER: farmsmart_user
      POSTGRES_PASSWORD: farmsmart_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U farmsmart_user -d farmsmart_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching (optional)
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # FarmSmart API
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************/farmsmart_db
      - SECRET_KEY=your-production-secret-key-here
      - OPENWEATHER_API_KEY=your-openweather-api-key
      - DEBUG=False
      - LOG_LEVEL=INFO
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./models:/app/models
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
