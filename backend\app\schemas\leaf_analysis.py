"""
Leaf analysis schemas for API validation
"""
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class LeafAnalysisBase(BaseModel):
    """Base leaf analysis schema"""
    crop_type: str = Field(..., max_length=100)
    crop_variety: Optional[str] = Field(None, max_length=100)
    growth_stage: Optional[str] = Field(None, max_length=50)
    field_name: Optional[str] = Field(None, max_length=255)
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)


class LeafAnalysisCreate(LeafAnalysisBase):
    """Schema for leaf analysis creation"""
    # Leaf Color Chart analysis
    lcc_reading: Optional[float] = Field(None, ge=1, le=6)
    lcc_category: Optional[str] = Field(None, regex="^(very_low|low|medium|high|very_high)$")
    
    # Leaf characteristics
    leaf_color_description: Optional[str] = Field(None, max_length=255)
    leaf_size: Optional[str] = Field(None, regex="^(small|medium|large)$")
    leaf_texture: Optional[str] = Field(None, regex="^(smooth|rough|waxy)$")
    
    # Deficiency indicators
    nitrogen_deficiency: bool = False
    phosphorus_deficiency: bool = False
    potassium_deficiency: bool = False
    iron_deficiency: bool = False
    magnesium_deficiency: bool = False
    
    # Severity assessment
    deficiency_severity: Optional[str] = Field(None, regex="^(mild|moderate|severe)$")
    affected_area_percentage: Optional[float] = Field(None, ge=0, le=100)
    
    # Environmental conditions
    weather_conditions: Optional[str] = Field(None, max_length=100)
    temperature_at_analysis: Optional[float] = None
    humidity_at_analysis: Optional[float] = Field(None, ge=0, le=100)
    
    # Analysis method
    analysis_method: str = Field("image_ai", regex="^(manual_lcc|image_ai|combined)$")
    device_used: Optional[str] = Field(None, max_length=100)
    
    # Additional observations
    pest_damage_visible: bool = False
    disease_symptoms: bool = False
    other_observations: Optional[str] = None
    notes: Optional[str] = None


class LeafAnalysisUpdate(BaseModel):
    """Schema for leaf analysis updates"""
    crop_variety: Optional[str] = Field(None, max_length=100)
    growth_stage: Optional[str] = Field(None, max_length=50)
    lcc_reading: Optional[float] = Field(None, ge=1, le=6)
    lcc_category: Optional[str] = Field(None, regex="^(very_low|low|medium|high|very_high)$")
    leaf_color_description: Optional[str] = Field(None, max_length=255)
    deficiency_severity: Optional[str] = Field(None, regex="^(mild|moderate|severe)$")
    affected_area_percentage: Optional[float] = Field(None, ge=0, le=100)
    analysis_validated: Optional[bool] = None
    validator_notes: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = Field(None, regex="^(active|archived)$")


class LeafAnalysisResponse(LeafAnalysisBase):
    """Schema for leaf analysis response"""
    id: int
    user_id: int
    analysis_date: datetime
    
    # Image information
    image_path: Optional[str]
    image_filename: Optional[str]
    image_size: Optional[int]
    
    # Analysis results
    lcc_reading: Optional[float]
    lcc_category: Optional[str]
    ai_nitrogen_level: Optional[float]
    ai_confidence_score: Optional[float]
    ai_color_values: Optional[str]
    
    # Leaf characteristics
    leaf_color_description: Optional[str]
    leaf_size: Optional[str]
    leaf_texture: Optional[str]
    
    # Deficiency indicators
    nitrogen_deficiency: bool
    phosphorus_deficiency: bool
    potassium_deficiency: bool
    iron_deficiency: bool
    magnesium_deficiency: bool
    
    # Severity and conditions
    deficiency_severity: Optional[str]
    affected_area_percentage: Optional[float]
    weather_conditions: Optional[str]
    temperature_at_analysis: Optional[float]
    humidity_at_analysis: Optional[float]
    
    # Analysis details
    analysis_method: str
    device_used: Optional[str]
    image_quality: str
    analysis_validated: bool
    validator_notes: Optional[str]
    
    # Observations
    pest_damage_visible: bool
    disease_symptoms: bool
    other_observations: Optional[str]
    
    # Status and timestamps
    status: str
    notes: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True
