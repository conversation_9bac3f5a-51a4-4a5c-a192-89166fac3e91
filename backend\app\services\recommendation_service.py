"""
AI recommendation service that combines soil, weather, and leaf data
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json
import numpy as np

from app.models.recommendation import Recommendation, RecommendationType
from app.models.soil_health import SoilHealthCard
from app.models.leaf_analysis import LeafAnalysis
from app.models.weather import WeatherData
from app.models.user import User
from app.schemas.recommendation import RecommendationCreate, RecommendationUpdate
from app.services.soil_health_service import SoilHealthService
from app.services.weather_service import WeatherService
from app.services.leaf_analysis_service import LeafAnalysisService


class RecommendationService:
    """Service class for AI-powered farming recommendations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.soil_service = SoilHealthService(db)
        self.weather_service = WeatherService(db)
        self.leaf_service = LeafAnalysisService(db)
    
    def create_recommendation(self, user_id: int, recommendation_data: RecommendationCreate) -> Recommendation:
        """Create a new recommendation"""
        db_recommendation = Recommendation(
            user_id=user_id,
            **recommendation_data.dict()
        )
        
        self.db.add(db_recommendation)
        self.db.commit()
        self.db.refresh(db_recommendation)
        return db_recommendation
    
    def get_recommendation(self, recommendation_id: int, user_id: int) -> Optional[Recommendation]:
        """Get recommendation by ID for a specific user"""
        return self.db.query(Recommendation).filter(
            Recommendation.id == recommendation_id,
            Recommendation.user_id == user_id
        ).first()
    
    def get_user_recommendations(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Recommendation]:
        """Get all recommendations for a user"""
        return self.db.query(Recommendation).filter(
            Recommendation.user_id == user_id
        ).order_by(Recommendation.created_at.desc()).offset(skip).limit(limit).all()
    
    def update_recommendation(self, recommendation_id: int, user_id: int, recommendation_data: RecommendationUpdate) -> Optional[Recommendation]:
        """Update recommendation"""
        recommendation = self.get_recommendation(recommendation_id, user_id)
        if not recommendation:
            return None
        
        update_data = recommendation_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(recommendation, field, value)
        
        recommendation.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(recommendation)
        return recommendation
    
    async def generate_comprehensive_recommendations(self, user_id: int, crop_type: str = None) -> List[Recommendation]:
        """Generate comprehensive recommendations using all available data sources"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return []
        
        # Get latest data from all sources
        soil_cards = self.soil_service.get_user_soil_health_cards(user_id, limit=1)
        leaf_analyses = self.leaf_service.get_user_leaf_analyses(user_id, limit=5)
        
        # Get weather data for user's location
        weather_data = []
        if user.latitude and user.longitude:
            weather_data = self.weather_service.get_weather_data(user.latitude, user.longitude)
        
        # Generate recommendations based on available data
        recommendations = []
        
        # Soil-based recommendations
        if soil_cards:
            soil_recommendations = await self._generate_soil_based_recommendations(
                user_id, soil_cards[0], crop_type, weather_data
            )
            recommendations.extend(soil_recommendations)
        
        # Leaf analysis-based recommendations
        if leaf_analyses:
            leaf_recommendations = await self._generate_leaf_based_recommendations(
                user_id, leaf_analyses, crop_type, weather_data
            )
            recommendations.extend(leaf_recommendations)
        
        # Weather-based recommendations
        if weather_data:
            weather_recommendations = await self._generate_weather_based_recommendations(
                user_id, weather_data, crop_type
            )
            recommendations.extend(weather_recommendations)
        
        # Integrated recommendations combining multiple data sources
        if soil_cards and leaf_analyses and weather_data:
            integrated_recommendations = await self._generate_integrated_recommendations(
                user_id, soil_cards[0], leaf_analyses[0], weather_data, crop_type
            )
            recommendations.extend(integrated_recommendations)
        
        # Save recommendations to database
        saved_recommendations = []
        for rec_data in recommendations:
            db_recommendation = Recommendation(**rec_data)
            self.db.add(db_recommendation)
            saved_recommendations.append(db_recommendation)
        
        self.db.commit()
        return saved_recommendations
    
    async def _generate_soil_based_recommendations(self, user_id: int, soil_card: SoilHealthCard, crop_type: str, weather_data: List[WeatherData]) -> List[Dict[str, Any]]:
        """Generate recommendations based on soil health data"""
        recommendations = []
        
        # Analyze soil health
        soil_analysis = self.soil_service.analyze_soil_health(soil_card)
        fertilizer_recs = self.soil_service.generate_fertilizer_recommendations(soil_card, crop_type)
        
        # Generate fertilizer recommendations
        for fert_rec in fertilizer_recs:
            # Check weather conditions for application timing
            timing_analysis = None
            if weather_data:
                timing_analysis = self.weather_service.analyze_application_timing(weather_data, "fertilizer")
            
            recommendation = {
                "user_id": user_id,
                "soil_health_card_id": soil_card.id,
                "recommendation_type": RecommendationType.FERTILIZER,
                "title": f"{fert_rec['fertilizer']} Application",
                "description": f"Apply {fert_rec['fertilizer']} based on soil analysis results",
                "crop_type": crop_type,
                "fertilizer_type": fert_rec['fertilizer'],
                "fertilizer_application_method": fert_rec['application_method'],
                "dosage_per_hectare": fert_rec['dosage_kg_ha'],
                "priority": self._determine_priority(fert_rec['type'], soil_analysis),
                "model_version": "soil_v1.0",
                "confidence_score": 0.8,
                "data_sources_used": json.dumps(["soil_health_card"]),
                "precautions": "Ensure soil moisture before application",
                "language": "en"
            }
            
            # Add timing information if weather data available
            if timing_analysis:
                recommendation["weather_conditions_required"] = json.dumps(timing_analysis)
                if timing_analysis["status"] == "poor":
                    recommendation["priority"] = "low"
                    recommendation["description"] += f". {timing_analysis['overall_recommendation']}"
            
            recommendations.append(recommendation)
        
        # pH correction recommendations
        ph_status = soil_analysis["ph_status"]
        if ph_status["status"] in ["acidic", "alkaline"]:
            recommendation = {
                "user_id": user_id,
                "soil_health_card_id": soil_card.id,
                "recommendation_type": RecommendationType.SOIL_MANAGEMENT,
                "title": f"Soil pH Correction - {ph_status['status'].title()}",
                "description": ph_status["recommendation"],
                "crop_type": crop_type,
                "priority": "medium",
                "model_version": "soil_v1.0",
                "confidence_score": 0.9,
                "data_sources_used": json.dumps(["soil_health_card"]),
                "language": "en"
            }
            recommendations.append(recommendation)
        
        return recommendations
    
    async def _generate_leaf_based_recommendations(self, user_id: int, leaf_analyses: List[LeafAnalysis], crop_type: str, weather_data: List[WeatherData]) -> List[Dict[str, Any]]:
        """Generate recommendations based on leaf analysis data"""
        recommendations = []
        
        # Use most recent leaf analysis
        latest_analysis = leaf_analyses[0]
        
        # Check for nitrogen deficiency
        if latest_analysis.nitrogen_deficiency:
            severity = latest_analysis.deficiency_severity or "moderate"
            
            # Determine dosage based on severity
            dosage_map = {"mild": 40, "moderate": 60, "severe": 100}
            dosage = dosage_map.get(severity, 60)
            
            # Check weather conditions
            timing_analysis = None
            if weather_data:
                timing_analysis = self.weather_service.analyze_application_timing(weather_data, "fertilizer")
            
            recommendation = {
                "user_id": user_id,
                "leaf_analysis_id": latest_analysis.id,
                "recommendation_type": RecommendationType.FERTILIZER,
                "title": f"Nitrogen Deficiency Treatment - {severity.title()}",
                "description": f"Leaf analysis indicates {severity} nitrogen deficiency. Immediate nitrogen application recommended.",
                "crop_type": crop_type or latest_analysis.crop_type,
                "growth_stage": latest_analysis.growth_stage,
                "nitrogen_amount": dosage,
                "fertilizer_type": "Urea (46-0-0)",
                "fertilizer_application_method": "Top dressing with irrigation",
                "dosage_per_hectare": dosage,
                "priority": "high" if severity == "severe" else "medium",
                "urgency_days": 3 if severity == "severe" else 7,
                "model_version": "leaf_v1.0",
                "confidence_score": latest_analysis.ai_confidence_score or 0.7,
                "data_sources_used": json.dumps(["leaf_analysis"]),
                "precautions": "Apply during cool hours to avoid leaf burn",
                "language": "en"
            }
            
            if timing_analysis:
                recommendation["weather_conditions_required"] = json.dumps(timing_analysis)
            
            recommendations.append(recommendation)
        
        # Check for other deficiencies
        deficiencies = []
        if latest_analysis.phosphorus_deficiency:
            deficiencies.append("phosphorus")
        if latest_analysis.potassium_deficiency:
            deficiencies.append("potassium")
        if latest_analysis.iron_deficiency:
            deficiencies.append("iron")
        if latest_analysis.magnesium_deficiency:
            deficiencies.append("magnesium")
        
        for deficiency in deficiencies:
            recommendation = {
                "user_id": user_id,
                "leaf_analysis_id": latest_analysis.id,
                "recommendation_type": RecommendationType.FERTILIZER,
                "title": f"{deficiency.title()} Deficiency Treatment",
                "description": f"Leaf analysis indicates {deficiency} deficiency",
                "crop_type": crop_type or latest_analysis.crop_type,
                "priority": "medium",
                "model_version": "leaf_v1.0",
                "confidence_score": 0.6,
                "data_sources_used": json.dumps(["leaf_analysis"]),
                "language": "en"
            }
            recommendations.append(recommendation)
        
        return recommendations
    
    async def _generate_weather_based_recommendations(self, user_id: int, weather_data: List[WeatherData], crop_type: str) -> List[Dict[str, Any]]:
        """Generate recommendations based on weather data"""
        recommendations = []
        
        # Irrigation recommendations
        irrigation_analysis = self.weather_service.get_irrigation_recommendations(weather_data, crop_type)
        
        if irrigation_analysis.get("week_rainfall", 0) < 15:
            recommendation = {
                "user_id": user_id,
                "recommendation_type": RecommendationType.IRRIGATION,
                "title": "Irrigation Required",
                "description": f"Low rainfall ({irrigation_analysis.get('week_rainfall', 0):.1f}mm) in past week. {irrigation_analysis.get('irrigation_frequency', 'Regular irrigation needed')}",
                "crop_type": crop_type,
                "priority": "high" if irrigation_analysis.get("week_rainfall", 0) < 5 else "medium",
                "urgency_days": 2,
                "model_version": "weather_v1.0",
                "confidence_score": 0.8,
                "data_sources_used": json.dumps(["weather_data"]),
                "weather_data_ids": json.dumps([w.id for w in weather_data[:7]]),
                "language": "en"
            }
            recommendations.append(recommendation)
        
        # Pest and disease risk based on weather
        current_weather = weather_data[0] if weather_data else None
        if current_weather:
            pest_risk = self._assess_pest_disease_risk(current_weather, weather_data)
            
            if pest_risk["risk_level"] in ["high", "very_high"]:
                recommendation = {
                    "user_id": user_id,
                    "recommendation_type": RecommendationType.PESTICIDE,
                    "title": f"Pest/Disease Risk Alert - {pest_risk['risk_level'].title()}",
                    "description": pest_risk["description"],
                    "crop_type": crop_type,
                    "priority": "high" if pest_risk["risk_level"] == "very_high" else "medium",
                    "urgency_days": 3,
                    "model_version": "weather_v1.0",
                    "confidence_score": 0.7,
                    "data_sources_used": json.dumps(["weather_data"]),
                    "weather_conditions_required": json.dumps(pest_risk),
                    "precautions": "Monitor crops closely for early signs",
                    "language": "en"
                }
                recommendations.append(recommendation)
        
        return recommendations
    
    async def _generate_integrated_recommendations(self, user_id: int, soil_card: SoilHealthCard, leaf_analysis: LeafAnalysis, weather_data: List[WeatherData], crop_type: str) -> List[Dict[str, Any]]:
        """Generate integrated recommendations combining all data sources"""
        recommendations = []
        
        # Comprehensive fertilizer recommendation
        soil_analysis = self.soil_service.analyze_soil_health(soil_card)
        timing_analysis = self.weather_service.analyze_application_timing(weather_data, "fertilizer")
        
        # Combine soil and leaf analysis for nitrogen recommendation
        soil_n_status = soil_analysis["nutrient_status"]["nitrogen"]["status"]
        leaf_n_deficiency = leaf_analysis.nitrogen_deficiency
        
        if soil_n_status == "low" or leaf_n_deficiency:
            # Calculate integrated dosage
            base_dosage = 80  # Base dosage
            
            # Adjust based on soil analysis
            if soil_n_status == "low":
                base_dosage += 20
            
            # Adjust based on leaf analysis
            if leaf_n_deficiency:
                severity_multiplier = {"mild": 1.0, "moderate": 1.2, "severe": 1.5}
                multiplier = severity_multiplier.get(leaf_analysis.deficiency_severity, 1.0)
                base_dosage = int(base_dosage * multiplier)
            
            # Adjust based on weather
            if timing_analysis["status"] == "poor":
                base_dosage *= 0.8  # Reduce dosage if weather is poor
            
            recommendation = {
                "user_id": user_id,
                "soil_health_card_id": soil_card.id,
                "leaf_analysis_id": leaf_analysis.id,
                "recommendation_type": RecommendationType.FERTILIZER,
                "title": "Integrated Nitrogen Management",
                "description": "Comprehensive nitrogen recommendation based on soil analysis, leaf color assessment, and weather conditions",
                "crop_type": crop_type or leaf_analysis.crop_type,
                "growth_stage": leaf_analysis.growth_stage,
                "nitrogen_amount": base_dosage,
                "fertilizer_type": "Urea (46-0-0)",
                "fertilizer_application_method": "Split application with weather consideration",
                "dosage_per_hectare": base_dosage,
                "priority": "high",
                "urgency_days": 5,
                "model_version": "integrated_v1.0",
                "confidence_score": 0.9,
                "data_sources_used": json.dumps(["soil_health_card", "leaf_analysis", "weather_data"]),
                "weather_conditions_required": json.dumps(timing_analysis),
                "weather_data_ids": json.dumps([w.id for w in weather_data[:3]]),
                "precautions": "Monitor weather conditions before application",
                "language": "en"
            }
            recommendations.append(recommendation)
        
        return recommendations
    
    def _determine_priority(self, nutrient_type: str, soil_analysis: Dict[str, Any]) -> str:
        """Determine priority based on nutrient type and soil analysis"""
        nutrient_status = soil_analysis["nutrient_status"].get(nutrient_type, {})
        status = nutrient_status.get("status", "medium")
        
        if status == "low":
            return "high"
        elif status == "medium":
            return "medium"
        else:
            return "low"
    
    def _assess_pest_disease_risk(self, current_weather: WeatherData, weather_history: List[WeatherData]) -> Dict[str, Any]:
        """Assess pest and disease risk based on weather conditions"""
        risk_factors = []
        risk_score = 0
        
        # High humidity increases fungal disease risk
        if current_weather.humidity and current_weather.humidity > 80:
            risk_factors.append("High humidity favors fungal diseases")
            risk_score += 2
        
        # Temperature range affects pest activity
        if current_weather.temperature and 25 <= current_weather.temperature <= 30:
            risk_factors.append("Optimal temperature for pest activity")
            risk_score += 1
        
        # Recent rainfall creates favorable conditions
        recent_rain = sum(w.rainfall or 0 for w in weather_history[:3])
        if recent_rain > 20:
            risk_factors.append("Recent rainfall increases disease pressure")
            risk_score += 2
        
        # Determine risk level
        if risk_score >= 4:
            risk_level = "very_high"
        elif risk_score >= 3:
            risk_level = "high"
        elif risk_score >= 2:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "description": f"Weather conditions indicate {risk_level} pest/disease risk. " + "; ".join(risk_factors)
        }
