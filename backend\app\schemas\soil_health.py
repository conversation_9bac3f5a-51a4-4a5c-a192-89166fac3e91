"""
Soil Health Card schemas for API validation
"""
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class SoilHealthCardBase(BaseModel):
    """Base soil health card schema"""
    card_number: Optional[str] = Field(None, max_length=50)
    field_name: Optional[str] = Field(None, max_length=255)
    sample_date: Optional[datetime] = None
    lab_name: Optional[str] = Field(None, max_length=255)
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)
    village: Optional[str] = Field(None, max_length=255)
    district: Optional[str] = Field(None, max_length=255)
    state: Optional[str] = Field(None, max_length=255)
    soil_type: Optional[str] = Field(None, max_length=100)
    soil_texture: Optional[str] = Field(None, max_length=100)


class SoilHealthCardCreate(SoilHealthCardBase):
    """Schema for soil health card creation"""
    # Chemical properties
    ph_level: Optional[float] = Field(None, ge=0, le=14)
    electrical_conductivity: Optional[float] = Field(None, ge=0)
    organic_carbon: Optional[float] = Field(None, ge=0, le=100)
    
    # Macronutrients (kg/ha)
    nitrogen_n: Optional[float] = Field(None, ge=0)
    phosphorus_p: Optional[float] = Field(None, ge=0)
    potassium_k: Optional[float] = Field(None, ge=0)
    
    # Secondary nutrients (kg/ha)
    sulfur_s: Optional[float] = Field(None, ge=0)
    calcium_ca: Optional[float] = Field(None, ge=0)
    magnesium_mg: Optional[float] = Field(None, ge=0)
    
    # Micronutrients (ppm)
    iron_fe: Optional[float] = Field(None, ge=0)
    manganese_mn: Optional[float] = Field(None, ge=0)
    zinc_zn: Optional[float] = Field(None, ge=0)
    copper_cu: Optional[float] = Field(None, ge=0)
    boron_b: Optional[float] = Field(None, ge=0)
    molybdenum_mo: Optional[float] = Field(None, ge=0)
    
    # Additional properties
    cation_exchange_capacity: Optional[float] = Field(None, ge=0)
    base_saturation: Optional[float] = Field(None, ge=0, le=100)
    
    # Lab recommendations and notes
    lab_recommendations: Optional[str] = None
    notes: Optional[str] = None


class SoilHealthCardUpdate(BaseModel):
    """Schema for soil health card updates"""
    field_name: Optional[str] = Field(None, max_length=255)
    ph_level: Optional[float] = Field(None, ge=0, le=14)
    electrical_conductivity: Optional[float] = Field(None, ge=0)
    organic_carbon: Optional[float] = Field(None, ge=0, le=100)
    nitrogen_n: Optional[float] = Field(None, ge=0)
    phosphorus_p: Optional[float] = Field(None, ge=0)
    potassium_k: Optional[float] = Field(None, ge=0)
    sulfur_s: Optional[float] = Field(None, ge=0)
    calcium_ca: Optional[float] = Field(None, ge=0)
    magnesium_mg: Optional[float] = Field(None, ge=0)
    iron_fe: Optional[float] = Field(None, ge=0)
    manganese_mn: Optional[float] = Field(None, ge=0)
    zinc_zn: Optional[float] = Field(None, ge=0)
    copper_cu: Optional[float] = Field(None, ge=0)
    boron_b: Optional[float] = Field(None, ge=0)
    molybdenum_mo: Optional[float] = Field(None, ge=0)
    cation_exchange_capacity: Optional[float] = Field(None, ge=0)
    base_saturation: Optional[float] = Field(None, ge=0, le=100)
    lab_recommendations: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = Field(None, regex="^(active|archived)$")


class SoilHealthCardResponse(SoilHealthCardBase):
    """Schema for soil health card response"""
    id: int
    user_id: int
    ph_level: Optional[float]
    electrical_conductivity: Optional[float]
    organic_carbon: Optional[float]
    nitrogen_n: Optional[float]
    phosphorus_p: Optional[float]
    potassium_k: Optional[float]
    sulfur_s: Optional[float]
    calcium_ca: Optional[float]
    magnesium_mg: Optional[float]
    iron_fe: Optional[float]
    manganese_mn: Optional[float]
    zinc_zn: Optional[float]
    copper_cu: Optional[float]
    boron_b: Optional[float]
    molybdenum_mo: Optional[float]
    cation_exchange_capacity: Optional[float]
    base_saturation: Optional[float]
    lab_recommendations: Optional[str]
    status: str
    notes: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True
