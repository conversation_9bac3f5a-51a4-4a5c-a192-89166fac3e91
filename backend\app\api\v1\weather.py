"""
Weather endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.schemas.weather import WeatherDataResponse, WeatherForecastResponse, WeatherRequest
from app.services.weather_service import WeatherService
from app.core.deps import get_current_active_user
from app.models.user import User

router = APIRouter()


@router.get("/current", response_model=WeatherDataResponse)
async def get_current_weather(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current weather data for specified location"""
    weather_service = WeatherService(db)
    
    try:
        # Fetch current weather from API
        weather_data = await weather_service.fetch_current_weather(latitude, longitude)
        if not weather_data:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Weather service unavailable"
            )
        
        # Store in database
        db_weather = weather_service.store_weather_data(weather_data, latitude, longitude, "current")
        return db_weather
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching weather data"
        )


@router.get("/forecast", response_model=WeatherForecastResponse)
async def get_weather_forecast(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    days: int = Query(5, ge=1, le=7),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get weather forecast for specified location"""
    weather_service = WeatherService(db)
    
    try:
        # Fetch current weather
        current_weather_data = await weather_service.fetch_current_weather(latitude, longitude)
        if not current_weather_data:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Weather service unavailable"
            )
        
        # Fetch forecast
        forecast_data = await weather_service.fetch_weather_forecast(latitude, longitude, days)
        if not forecast_data:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Weather forecast service unavailable"
            )
        
        # Store current weather
        current_db_weather = weather_service.store_weather_data(current_weather_data, latitude, longitude, "current")
        
        # Store forecast data
        forecast_db_weather = []
        for forecast_item in forecast_data.get("list", []):
            db_weather = weather_service.store_weather_data(forecast_item, latitude, longitude, "forecast")
            forecast_db_weather.append(db_weather)
        
        return {
            "current": current_db_weather,
            "forecast": forecast_db_weather,
            "location": current_weather_data.get("name", "Unknown"),
            "last_updated": current_db_weather.created_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching weather forecast"
        )


@router.get("/history", response_model=List[WeatherDataResponse])
def get_weather_history(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    days_back: int = Query(7, ge=1, le=30),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get historical weather data for specified location"""
    weather_service = WeatherService(db)
    
    weather_data = weather_service.get_weather_data(latitude, longitude, days_back)
    return weather_data


@router.get("/application-timing")
async def get_application_timing(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    application_type: str = Query("fertilizer", regex="^(fertilizer|pesticide)$"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get optimal timing analysis for fertilizer/pesticide application"""
    weather_service = WeatherService(db)
    
    try:
        # Get recent weather data
        weather_data = weather_service.get_weather_data(latitude, longitude, 7)
        
        if not weather_data:
            # Fetch current weather if no historical data
            current_weather_data = await weather_service.fetch_current_weather(latitude, longitude)
            if current_weather_data:
                db_weather = weather_service.store_weather_data(current_weather_data, latitude, longitude, "current")
                weather_data = [db_weather]
        
        if not weather_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No weather data available for this location"
            )
        
        # Analyze application timing
        timing_analysis = weather_service.analyze_application_timing(weather_data, application_type)
        
        return {
            "location": {"latitude": latitude, "longitude": longitude},
            "application_type": application_type,
            "analysis": timing_analysis,
            "weather_data_points": len(weather_data)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error analyzing application timing"
        )


@router.get("/irrigation-recommendations")
def get_irrigation_recommendations(
    latitude: float = Query(..., ge=-90, le=90),
    longitude: float = Query(..., ge=-180, le=180),
    crop_type: str = Query("general"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get irrigation recommendations based on weather data"""
    weather_service = WeatherService(db)
    
    # Get recent weather data
    weather_data = weather_service.get_weather_data(latitude, longitude, 7)
    
    if not weather_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No weather data available for this location"
        )
    
    # Get irrigation recommendations
    irrigation_recs = weather_service.get_irrigation_recommendations(weather_data, crop_type)
    
    return {
        "location": {"latitude": latitude, "longitude": longitude},
        "crop_type": crop_type,
        "recommendations": irrigation_recs,
        "weather_data_points": len(weather_data)
    }


@router.post("/refresh")
async def refresh_weather_data(
    weather_request: WeatherRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Refresh weather data for specified location"""
    weather_service = WeatherService(db)
    
    try:
        # Fetch and store current weather
        current_weather_data = await weather_service.fetch_current_weather(
            weather_request.latitude, weather_request.longitude
        )
        
        if not current_weather_data:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Weather service unavailable"
            )
        
        # Store current weather
        current_db_weather = weather_service.store_weather_data(
            current_weather_data, weather_request.latitude, weather_request.longitude, "current"
        )
        
        # Fetch and store forecast if requested
        forecast_db_weather = []
        if weather_request.days > 1:
            forecast_data = await weather_service.fetch_weather_forecast(
                weather_request.latitude, weather_request.longitude, weather_request.days
            )
            
            if forecast_data:
                for forecast_item in forecast_data.get("list", []):
                    db_weather = weather_service.store_weather_data(
                        forecast_item, weather_request.latitude, weather_request.longitude, "forecast"
                    )
                    forecast_db_weather.append(db_weather)
        
        return {
            "message": "Weather data refreshed successfully",
            "current_weather": current_db_weather,
            "forecast_points": len(forecast_db_weather),
            "location": current_weather_data.get("name", "Unknown")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error refreshing weather data"
        )
