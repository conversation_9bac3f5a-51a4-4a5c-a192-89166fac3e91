"""
Tests for authentication endpoints
"""
import pytest
from fastapi import status


class TestAuth:
    """Test authentication functionality"""
    
    def test_register_user(self, client):
        """Test user registration"""
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "password": "newpass123",
            "full_name": "New User",
            "user_type": "farmer"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["username"] == user_data["username"]
        assert data["full_name"] == user_data["full_name"]
        assert "id" in data
    
    def test_register_duplicate_email(self, client, test_user):
        """Test registration with duplicate email"""
        user_data = {
            "email": "<EMAIL>",  # Same as test_user
            "username": "newuser",
            "password": "newpass123",
            "full_name": "New User",
            "user_type": "farmer"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Email already registered" in response.json()["detail"]
    
    def test_register_duplicate_username(self, client, test_user):
        """Test registration with duplicate username"""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",  # Same as test_user
            "password": "newpass123",
            "full_name": "New User",
            "user_type": "farmer"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Username already taken" in response.json()["detail"]
    
    def test_login_success(self, client, test_user):
        """Test successful login"""
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        response = client.post("/api/v1/auth/login-json", json=login_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
    
    def test_login_invalid_username(self, client):
        """Test login with invalid username"""
        login_data = {
            "username": "nonexistent",
            "password": "testpass123"
        }
        
        response = client.post("/api/v1/auth/login-json", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Incorrect username or password" in response.json()["detail"]
    
    def test_login_invalid_password(self, client, test_user):
        """Test login with invalid password"""
        login_data = {
            "username": "testuser",
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login-json", json=login_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Incorrect username or password" in response.json()["detail"]
    
    def test_login_form_data(self, client, test_user):
        """Test login with form data"""
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_verify_email(self, client, test_user):
        """Test email verification"""
        response = client.post(f"/api/v1/auth/verify-email/{test_user.id}")
        
        assert response.status_code == status.HTTP_200_OK
        assert "Email verified successfully" in response.json()["message"]
    
    def test_verify_email_invalid_user(self, client):
        """Test email verification with invalid user ID"""
        response = client.post("/api/v1/auth/verify-email/999")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found" in response.json()["detail"]
    
    def test_change_password(self, client, test_user):
        """Test password change"""
        response = client.post(
            f"/api/v1/auth/change-password/{test_user.id}",
            params={"new_password": "newpassword123"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert "Password changed successfully" in response.json()["message"]
    
    def test_change_password_invalid_user(self, client):
        """Test password change with invalid user ID"""
        response = client.post(
            "/api/v1/auth/change-password/999",
            params={"new_password": "newpassword123"}
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found" in response.json()["detail"]


class TestAuthValidation:
    """Test authentication input validation"""
    
    def test_register_invalid_email(self, client):
        """Test registration with invalid email"""
        user_data = {
            "email": "invalid-email",
            "username": "testuser",
            "password": "testpass123",
            "full_name": "Test User",
            "user_type": "farmer"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_register_short_username(self, client):
        """Test registration with short username"""
        user_data = {
            "email": "<EMAIL>",
            "username": "ab",  # Too short
            "password": "testpass123",
            "full_name": "Test User",
            "user_type": "farmer"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_register_short_password(self, client):
        """Test registration with short password"""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "short",  # Too short
            "full_name": "Test User",
            "user_type": "farmer"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_register_invalid_user_type(self, client):
        """Test registration with invalid user type"""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "testpass123",
            "full_name": "Test User",
            "user_type": "invalid_type"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
