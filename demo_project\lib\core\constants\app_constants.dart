/// Application constants and configuration
class AppConstants {
  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const String apiVersion = 'v1';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';
  
  // App Information
  static const String appName = 'FarmSmart AI';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Precision Agriculture App for Soil Health & Crop Optimization';
  
  // Image Configuration
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png'];
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration cacheTimeout = Duration(hours: 1);
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 100;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 50;
  
  // Soil Health Ranges
  static const double minPH = 0.0;
  static const double maxPH = 14.0;
  static const double optimalPHMin = 6.0;
  static const double optimalPHMax = 7.5;
  
  // Leaf Color Chart
  static const double minLCCReading = 1.0;
  static const double maxLCCReading = 6.0;
  
  // Weather
  static const int weatherForecastDays = 7;
  static const Duration weatherCacheTimeout = Duration(hours: 1);
  
  // Recommendations
  static const List<String> priorityLevels = ['low', 'medium', 'high', 'urgent'];
  static const List<String> recommendationTypes = [
    'fertilizer',
    'pesticide',
    'irrigation',
    'planting',
    'harvesting',
    'soil_management',
    'crop_selection',
    'general'
  ];
  
  // User Types
  static const List<String> userTypes = ['farmer', 'advisor', 'admin'];
  
  // Growing Seasons
  static const List<String> growingSeasons = ['Kharif', 'Rabi', 'Zaid', 'Annual'];
  
  // Crop Families
  static const List<String> cropFamilies = [
    'Cereals',
    'Legumes',
    'Vegetables',
    'Fruits',
    'Fiber crops',
    'Sugar crops',
    'Oil seeds',
    'Spices',
    'Fodder crops'
  ];
}
