import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../theme/app_theme.dart';
import '../services/offline_service.dart';

/// Widget to display sync status and controls
class SyncStatusWidget extends ConsumerWidget {
  const SyncStatusWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncStatusAsync = ref.watch(syncStatusProvider);
    final connectivityAsync = ref.watch(connectivityProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.cloudSync,
                  color: AppTheme.primaryGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Data Sync Status',
                  style: AppTheme.titleMedium.copyWith(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Connection Status
            connectivityAsync.when(
              data: (connectivity) => _buildConnectionStatus(connectivity),
              loading: () => _buildLoadingIndicator(),
              error: (error, stack) => _buildErrorIndicator(),
            ),

            const SizedBox(height: 12),

            // Sync Status
            syncStatusAsync.when(
              data: (status) => _buildSyncStatus(context, ref, status),
              loading: () => _buildLoadingIndicator(),
              error: (error, stack) => _buildErrorIndicator(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus(List<ConnectivityResult> connectivity) {
    final isOnline =
        connectivity.isNotEmpty &&
        !connectivity.contains(ConnectivityResult.none);

    return Row(
      children: [
        Icon(
          isOnline ? MdiIcons.wifi : MdiIcons.wifiOff,
          color: isOnline ? AppTheme.successGreen : AppTheme.errorRed,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          isOnline ? 'Online' : 'Offline',
          style: AppTheme.bodyMedium.copyWith(
            color: isOnline ? AppTheme.successGreen : AppTheme.errorRed,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildSyncStatus(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> status,
  ) {
    final needsSync = status['needsSync'] as bool;
    final pendingItems = status['pendingItems'] as int;
    final lastSync = status['lastSync'] as DateTime?;
    final isOnline = status['isOnline'] as bool;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sync Status Row
        Row(
          children: [
            Icon(
              needsSync ? MdiIcons.syncAlert : MdiIcons.syncCircle,
              color: needsSync ? AppTheme.warningOrange : AppTheme.successGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                needsSync
                    ? '$pendingItems items pending sync'
                    : 'All data synced',
                style: AppTheme.bodyMedium.copyWith(
                  color: needsSync
                      ? AppTheme.warningOrange
                      : AppTheme.successGreen,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),

        if (lastSync != null) ...[
          const SizedBox(height: 8),
          Text(
            'Last synced: ${_formatDateTime(lastSync)}',
            style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
          ),
        ],

        if (needsSync && isOnline) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _syncNow(context, ref),
              icon: Icon(MdiIcons.cloudSync, size: 18),
              label: const Text('Sync Now'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: AppTheme.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],

        if (!isOnline && needsSync) ...[
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.warningOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.warningOrange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(MdiIcons.wifiOff, color: AppTheme.warningOrange, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Connect to internet to sync data',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.warningOrange,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLoadingIndicator() {
    return Row(
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Loading...',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }

  Widget _buildErrorIndicator() {
    return Row(
      children: [
        Icon(MdiIcons.alertCircle, color: AppTheme.errorRed, size: 20),
        const SizedBox(width: 8),
        Text(
          'Error loading status',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.errorRed),
        ),
      ],
    );
  }

  Future<void> _syncNow(BuildContext context, WidgetRef ref) async {
    final offlineService = ref.read(offlineServiceProvider);

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryGreen,
                ),
              ),
              const SizedBox(width: 16),
              const Text('Syncing data...'),
            ],
          ),
        ),
      );

      final success = await offlineService.syncPendingData();

      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Data synced successfully!'
                  : 'Sync failed. Please try again.',
            ),
            backgroundColor: success
                ? AppTheme.successGreen
                : AppTheme.errorRed,
          ),
        );

        // Refresh the sync status
        ref.invalidate(syncStatusProvider);
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync error: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
