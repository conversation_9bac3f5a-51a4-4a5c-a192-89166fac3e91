import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/app_drawer.dart';
import '../../../../core/widgets/bottom_navigation.dart';
import '../../../auth/data/providers/auth_provider.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/weather_summary_card.dart';
import '../widgets/recent_activities_card.dart';
import '../widgets/quick_actions_card.dart';
import '../widgets/recommendations_summary_card.dart';

/// Main dashboard screen showing overview of farm data
class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: const Text('FarmSmart AI'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.bell),
            onPressed: () {
              // TODO: Show notifications
              _showNotifications();
            },
          ),
          IconButton(
            icon: Icon(MdiIcons.account),
            onPressed: () => context.go(AppRouter.profile),
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Quick Stats
              _buildQuickStats(),

              const SizedBox(height: 24),

              // Weather Summary
              const WeatherSummaryCard(),

              const SizedBox(height: 16),

              // Quick Actions
              const QuickActionsCard(),

              const SizedBox(height: 16),

              // Recommendations Summary
              const RecommendationsSummaryCard(),

              const SizedBox(height: 16),

              // Recent Activities
              const RecentActivitiesCard(),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const AppBottomNavigation(currentIndex: 0),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActionMenu();
        },
        child: Icon(MdiIcons.plus),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final user = ref.watch(currentUserProvider);
    final userName = user?.fullName?.split(' ').firstOrNull ?? 'User';
    final farmName = user?.farmName ?? 'Your Farm';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppTheme.primaryGreen, AppTheme.lightGreen],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppTheme.textOnPrimary,
                child: Icon(
                  MdiIcons.account,
                  size: 30,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back, $userName!',
                      style: AppTheme.titleLarge.copyWith(
                        color: AppTheme.textOnPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      farmName,
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textOnPrimary.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Your farm is looking healthy! Check out the latest recommendations below.',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textOnPrimary.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Quick Overview', style: AppTheme.titleLarge),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DashboardCard(
                title: 'Soil Health Cards',
                value: '3',
                icon: MdiIcons.testTube,
                color: AppTheme.primaryBrown,
                onTap: () => context.go(AppRouter.soilHealth),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DashboardCard(
                title: 'Leaf Analyses',
                value: '7',
                icon: MdiIcons.leaf,
                color: AppTheme.accentGreen,
                onTap: () => context.go(AppRouter.leafAnalysis),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DashboardCard(
                title: 'Active Recommendations',
                value: '5',
                icon: MdiIcons.lightbulbOn,
                color: AppTheme.warningOrange,
                onTap: () => context.go(AppRouter.recommendations),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DashboardCard(
                title: 'Farm Size',
                value: '10.5 acres',
                icon: MdiIcons.ruler,
                color: AppTheme.infoBlue,
                onTap: () => context.go(AppRouter.profile),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _refreshData() async {
    // TODO: Implement data refresh
    await Future.delayed(const Duration(seconds: 2));
  }

  void _showNotifications() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(MdiIcons.bell, color: AppTheme.primaryGreen),
                const SizedBox(width: 8),
                Text('Notifications', style: AppTheme.titleLarge),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: Mark all as read
                  },
                  child: const Text('Mark all as read'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // TODO: Add notification list
            const ListTile(
              leading: Icon(Icons.info, color: AppTheme.infoBlue),
              title: Text('Weather Alert'),
              subtitle: Text('Rain expected in next 24 hours'),
              trailing: Text('2h ago'),
            ),
            const ListTile(
              leading: Icon(Icons.warning, color: AppTheme.warningOrange),
              title: Text('Fertilizer Recommendation'),
              subtitle: Text('Nitrogen deficiency detected in Field A'),
              trailing: Text('1d ago'),
            ),
            const ListTile(
              leading: Icon(Icons.check_circle, color: AppTheme.successGreen),
              title: Text('Analysis Complete'),
              subtitle: Text('Soil health card analysis ready'),
              trailing: Text('2d ago'),
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickActionMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Quick Actions', style: AppTheme.titleLarge),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: [
                _buildQuickActionItem(
                  icon: MdiIcons.testTube,
                  label: 'Add Soil Card',
                  onTap: () {
                    Navigator.pop(context);
                    context.go(AppRouter.soilHealthForm);
                  },
                ),
                _buildQuickActionItem(
                  icon: MdiIcons.leaf,
                  label: 'Leaf Analysis',
                  onTap: () {
                    Navigator.pop(context);
                    context.go(AppRouter.leafAnalysisForm);
                  },
                ),
                _buildQuickActionItem(
                  icon: MdiIcons.weatherSunny,
                  label: 'Weather',
                  onTap: () {
                    Navigator.pop(context);
                    context.go(AppRouter.weather);
                  },
                ),
                _buildQuickActionItem(
                  icon: MdiIcons.sprout,
                  label: 'Crops',
                  onTap: () {
                    Navigator.pop(context);
                    context.go(AppRouter.crops);
                  },
                ),
                _buildQuickActionItem(
                  icon: MdiIcons.lightbulbOn,
                  label: 'Recommendations',
                  onTap: () {
                    Navigator.pop(context);
                    context.go(AppRouter.recommendations);
                  },
                ),
                _buildQuickActionItem(
                  icon: MdiIcons.cog,
                  label: 'Settings',
                  onTap: () {
                    Navigator.pop(context);
                    context.go(AppRouter.settings);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: AppTheme.primaryGreen),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTheme.labelMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
