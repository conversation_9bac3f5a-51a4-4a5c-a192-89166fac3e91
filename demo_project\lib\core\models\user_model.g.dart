// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: (json['id'] as num).toInt(),
      email: json['email'] as String,
      username: json['username'] as String,
      fullName: json['full_name'] as String,
      phoneNumber: json['phone_number'] as String?,
      farmName: json['farm_name'] as String?,
      farmLocation: json['farm_location'] as String?,
      farmSizeAcres: (json['farm_size_acres'] as num?)?.toDouble(),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      preferredLanguage: json['preferred_language'] as String? ?? 'en',
      userType: json['user_type'] as String? ?? 'farmer',
      isActive: json['is_active'] as bool? ?? true,
      isVerified: json['is_verified'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      lastLogin: json['last_login'] == null
          ? null
          : DateTime.parse(json['last_login'] as String),
      profilePicture: json['profile_picture'] as String?,
      bio: json['bio'] as String?,
      yearsExperience: (json['years_experience'] as num?)?.toInt(),
      specializations: (json['specializations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      notificationsEnabled: json['notifications_enabled'] as bool? ?? true,
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'username': instance.username,
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'farm_name': instance.farmName,
      'farm_location': instance.farmLocation,
      'farm_size_acres': instance.farmSizeAcres,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'preferred_language': instance.preferredLanguage,
      'user_type': instance.userType,
      'is_active': instance.isActive,
      'is_verified': instance.isVerified,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'last_login': instance.lastLogin?.toIso8601String(),
      'profile_picture': instance.profilePicture,
      'bio': instance.bio,
      'years_experience': instance.yearsExperience,
      'specializations': instance.specializations,
      'notifications_enabled': instance.notificationsEnabled,
    };

UserRegistration _$UserRegistrationFromJson(Map<String, dynamic> json) =>
    UserRegistration(
      email: json['email'] as String,
      username: json['username'] as String,
      password: json['password'] as String,
      fullName: json['full_name'] as String,
      phoneNumber: json['phone_number'] as String?,
      farmName: json['farm_name'] as String?,
      farmLocation: json['farm_location'] as String?,
      farmSizeAcres: (json['farm_size_acres'] as num?)?.toDouble(),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      preferredLanguage: json['preferred_language'] as String? ?? 'en',
      userType: json['user_type'] as String? ?? 'farmer',
    );

Map<String, dynamic> _$UserRegistrationToJson(UserRegistration instance) =>
    <String, dynamic>{
      'email': instance.email,
      'username': instance.username,
      'password': instance.password,
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'farm_name': instance.farmName,
      'farm_location': instance.farmLocation,
      'farm_size_acres': instance.farmSizeAcres,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'preferred_language': instance.preferredLanguage,
      'user_type': instance.userType,
    };

UserLogin _$UserLoginFromJson(Map<String, dynamic> json) => UserLogin(
      username: json['username'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$UserLoginToJson(UserLogin instance) => <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
    };

AuthToken _$AuthTokenFromJson(Map<String, dynamic> json) => AuthToken(
      accessToken: json['access_token'] as String,
      tokenType: json['token_type'] as String,
      expiresIn: (json['expires_in'] as num).toInt(),
    );

Map<String, dynamic> _$AuthTokenToJson(AuthToken instance) => <String, dynamic>{
      'access_token': instance.accessToken,
      'token_type': instance.tokenType,
      'expires_in': instance.expiresIn,
    };

UserUpdate _$UserUpdateFromJson(Map<String, dynamic> json) => UserUpdate(
      fullName: json['full_name'] as String?,
      phoneNumber: json['phone_number'] as String?,
      farmName: json['farm_name'] as String?,
      farmLocation: json['farm_location'] as String?,
      farmSizeAcres: (json['farm_size_acres'] as num?)?.toDouble(),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      preferredLanguage: json['preferred_language'] as String?,
      profilePicture: json['profile_picture'] as String?,
      bio: json['bio'] as String?,
      yearsExperience: (json['years_experience'] as num?)?.toInt(),
      specializations: (json['specializations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      notificationsEnabled: json['notifications_enabled'] as bool?,
    );

Map<String, dynamic> _$UserUpdateToJson(UserUpdate instance) =>
    <String, dynamic>{
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'farm_name': instance.farmName,
      'farm_location': instance.farmLocation,
      'farm_size_acres': instance.farmSizeAcres,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'preferred_language': instance.preferredLanguage,
      'profile_picture': instance.profilePicture,
      'bio': instance.bio,
      'years_experience': instance.yearsExperience,
      'specializations': instance.specializations,
      'notifications_enabled': instance.notificationsEnabled,
    };
