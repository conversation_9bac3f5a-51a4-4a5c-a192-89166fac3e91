"""
Tests for soil health endpoints
"""
import pytest
from fastapi import status


class TestSoilHealth:
    """Test soil health functionality"""
    
    def test_create_soil_health_card(self, client, auth_headers, sample_soil_health_data):
        """Test creating soil health card"""
        response = client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["card_number"] == sample_soil_health_data["card_number"]
        assert data["field_name"] == sample_soil_health_data["field_name"]
        assert data["ph_level"] == sample_soil_health_data["ph_level"]
        assert "id" in data
        assert "user_id" in data
    
    def test_create_soil_health_card_unauthorized(self, client, sample_soil_health_data):
        """Test creating soil health card without authentication"""
        response = client.post("/api/v1/soil-health/", json=sample_soil_health_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_get_user_soil_health_cards(self, client, auth_headers, sample_soil_health_data):
        """Test getting user's soil health cards"""
        # First create a card
        client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        
        # Then get all cards
        response = client.get("/api/v1/soil-health/", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert data[0]["card_number"] == sample_soil_health_data["card_number"]
    
    def test_get_specific_soil_health_card(self, client, auth_headers, sample_soil_health_data):
        """Test getting specific soil health card"""
        # Create a card
        create_response = client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        card_id = create_response.json()["id"]
        
        # Get the specific card
        response = client.get(f"/api/v1/soil-health/{card_id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == card_id
        assert data["card_number"] == sample_soil_health_data["card_number"]
    
    def test_get_nonexistent_soil_health_card(self, client, auth_headers):
        """Test getting non-existent soil health card"""
        response = client.get("/api/v1/soil-health/999", headers=auth_headers)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "Soil health card not found" in response.json()["detail"]
    
    def test_update_soil_health_card(self, client, auth_headers, sample_soil_health_data):
        """Test updating soil health card"""
        # Create a card
        create_response = client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        card_id = create_response.json()["id"]
        
        # Update the card
        update_data = {"ph_level": 7.0, "notes": "Updated pH level"}
        response = client.put(
            f"/api/v1/soil-health/{card_id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["ph_level"] == 7.0
        assert data["notes"] == "Updated pH level"
    
    def test_analyze_soil_health(self, client, auth_headers, sample_soil_health_data):
        """Test soil health analysis"""
        # Create a card
        create_response = client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        card_id = create_response.json()["id"]
        
        # Get analysis
        response = client.get(f"/api/v1/soil-health/{card_id}/analysis", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "overall_health" in data
        assert "ph_status" in data
        assert "nutrient_status" in data
        assert "organic_matter_status" in data
    
    def test_get_fertilizer_recommendations(self, client, auth_headers, sample_soil_health_data):
        """Test getting fertilizer recommendations"""
        # Create a card
        create_response = client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        card_id = create_response.json()["id"]
        
        # Get recommendations
        response = client.get(
            f"/api/v1/soil-health/{card_id}/recommendations",
            params={"crop_type": "rice"},
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "card_id" in data
        assert "crop_type" in data
        assert "recommendations" in data
        assert isinstance(data["recommendations"], list)
    
    def test_delete_soil_health_card(self, client, auth_headers, sample_soil_health_data):
        """Test archiving soil health card"""
        # Create a card
        create_response = client.post(
            "/api/v1/soil-health/",
            json=sample_soil_health_data,
            headers=auth_headers
        )
        card_id = create_response.json()["id"]
        
        # Archive the card
        response = client.delete(f"/api/v1/soil-health/{card_id}", headers=auth_headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "archived successfully" in response.json()["message"]


class TestSoilHealthValidation:
    """Test soil health input validation"""
    
    def test_invalid_ph_level(self, client, auth_headers):
        """Test creating card with invalid pH level"""
        invalid_data = {
            "card_number": "SHC001",
            "ph_level": 15.0,  # Invalid pH (>14)
            "field_name": "Test Field"
        }
        
        response = client.post(
            "/api/v1/soil-health/",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_negative_nutrient_values(self, client, auth_headers):
        """Test creating card with negative nutrient values"""
        invalid_data = {
            "card_number": "SHC001",
            "nitrogen_n": -10,  # Negative value
            "field_name": "Test Field"
        }
        
        response = client.post(
            "/api/v1/soil-health/",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_invalid_coordinates(self, client, auth_headers):
        """Test creating card with invalid coordinates"""
        invalid_data = {
            "card_number": "SHC001",
            "latitude": 100.0,  # Invalid latitude (>90)
            "longitude": 200.0,  # Invalid longitude (>180)
            "field_name": "Test Field"
        }
        
        response = client.post(
            "/api/v1/soil-health/",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
