import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/bottom_navigation.dart';

/// Weather screen
class WeatherScreen extends ConsumerWidget {
  const WeatherScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Weather'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.refresh),
            onPressed: () {
              // TODO: Refresh weather data
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Current Weather Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Icon(MdiIcons.weatherSunny, size: 64, color: AppTheme.warningOrange),
                    const SizedBox(height: 16),
                    Text('28°C', style: AppTheme.headlineLarge),
                    Text('Sunny', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildWeatherDetail(MdiIcons.waterPercent, 'Humidity', '65%'),
                        _buildWeatherDetail(MdiIcons.weatherWindy, 'Wind', '12 km/h'),
                        _buildWeatherDetail(MdiIcons.weatherRainy, 'Rain', '0mm'),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 7-Day Forecast
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('7-Day Forecast', style: AppTheme.titleMedium),
                    const SizedBox(height: 16),
                    ...List.generate(7, (index) => _buildForecastItem(index)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const AppBottomNavigation(currentIndex: 2),
    );
  }

  Widget _buildWeatherDetail(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryGreen),
        const SizedBox(height: 4),
        Text(value, style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600)),
        Text(label, style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary)),
      ],
    );
  }

  Widget _buildForecastItem(int index) {
    final days = ['Today', 'Tomorrow', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final temps = ['28°/18°', '30°/20°', '27°/17°', '25°/15°', '26°/16°', '29°/19°', '31°/21°'];
    final icons = [
      MdiIcons.weatherSunny,
      MdiIcons.weatherPartlyCloudy,
      MdiIcons.weatherRainy,
      MdiIcons.weatherCloudy,
      MdiIcons.weatherSunny,
      MdiIcons.weatherPartlyCloudy,
      MdiIcons.weatherSunny,
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(days[index], style: AppTheme.bodyMedium),
          ),
          Icon(icons[index], color: AppTheme.primaryGreen),
          const Spacer(),
          Text(temps[index], style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}
