import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/constants/app_constants.dart';

/// Authentication service for handling API calls
class AuthService {
  // Store registered users in memory for demo (in real app, this would be API calls)
  static final Map<String, Map<String, dynamic>> _registeredUsers = {};
  static int _nextUserId = 1;

  /// Login user with username/email and password
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      // For demo purposes, simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Check if user exists in our registered users
      final userKey = username.toLowerCase();
      if (_registeredUsers.containsKey(userKey)) {
        final userData = _registeredUsers[userKey]!;

        // Check password (in real app, this would be hashed)
        if (userData['password'] == password) {
          // Create user object from stored data
          final user = User(
            id: userData['id'],
            email: userData['email'],
            username: userData['username'],
            fullName: userData['fullName'],
            phoneNumber: userData['phoneNumber'],
            farmName: userData['farmName'],
            farmLocation: userData['farmLocation'],
            farmSizeAcres: userData['farmSizeAcres'],
            latitude: userData['latitude'],
            longitude: userData['longitude'],
            preferredLanguage: userData['preferredLanguage'] ?? 'en',
            userType: userData['userType'] ?? 'farmer',
            isActive: true,
            isVerified: true,
            createdAt: DateTime.parse(userData['createdAt']),
            updatedAt: DateTime.now(),
            lastLogin: DateTime.now(),
          );

          return {
            'success': true,
            'user': user,
            'token': 'demo_token_${DateTime.now().millisecondsSinceEpoch}',
            'message': 'Login successful',
          };
        } else {
          return {'success': false, 'message': 'Invalid password'};
        }
      } else {
        return {
          'success': false,
          'message': 'User not found. Please register first.',
        };
      }

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.post(
        '/auth/login',
        data: {
          'username': username,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final user = User.fromJson(data['user']);
        
        return {
          'success': true,
          'user': user,
          'token': data['access_token'],
          'message': 'Login successful',
        };
      } else {
        return {
          'success': false,
          'message': response.data['detail'] ?? 'Login failed',
        };
      }
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Register new user
  Future<Map<String, dynamic>> register(UserRegistration registration) async {
    try {
      // For demo purposes, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Check if user already exists
      final userKey = registration.email.toLowerCase();
      final usernameKey = registration.username.toLowerCase();

      if (_registeredUsers.containsKey(userKey) ||
          _registeredUsers.values.any(
            (user) => user['username'].toLowerCase() == usernameKey,
          )) {
        return {
          'success': false,
          'message': 'User with this email or username already exists.',
        };
      }

      // Store user data
      final userData = {
        'id': _nextUserId++,
        'email': registration.email,
        'username': registration.username,
        'password': registration.password, // In real app, this would be hashed
        'fullName': registration.fullName,
        'phoneNumber': registration.phoneNumber,
        'farmName': registration.farmName,
        'farmLocation': registration.farmLocation,
        'farmSizeAcres': registration.farmSizeAcres,
        'latitude': null,
        'longitude': null,
        'preferredLanguage': 'en',
        'userType': registration.userType,
        'createdAt': DateTime.now().toIso8601String(),
      };

      _registeredUsers[userKey] = userData;

      return {
        'success': true,
        'message':
            'Registration successful. You can now login with your credentials.',
      };

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.post(
        '/auth/register',
        data: registration.toJson(),
      );

      if (response.statusCode == 201) {
        return {
          'success': true,
          'message': 'Registration successful',
        };
      } else {
        return {
          'success': false,
          'message': response.data['detail'] ?? 'Registration failed',
        };
      }
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Get current user profile
  Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      // For demo purposes, return mock user
      await Future.delayed(const Duration(seconds: 1));

      final mockUser = User(
        id: 1,
        email: '<EMAIL>',
        username: 'johnfarmer',
        fullName: 'John Farmer',
        phoneNumber: '+91-9876543210',
        farmName: 'Green Valley Farm',
        farmLocation: 'Punjab, India',
        farmSizeAcres: 10.5,
        latitude: 30.7333,
        longitude: 76.7794,
        preferredLanguage: 'en',
        userType: 'farmer',
        isActive: true,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        lastLogin: DateTime.now(),
      );

      return {'success': true, 'user': mockUser};

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.get('/auth/me');

      if (response.statusCode == 200) {
        final user = User.fromJson(response.data);
        return {
          'success': true,
          'user': user,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to get user data',
        };
      }
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateProfile(UserUpdate update) async {
    try {
      // For demo purposes, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock updated user
      final updatedUser = User(
        id: 1,
        email: '<EMAIL>',
        username: 'johnfarmer',
        fullName: update.fullName ?? 'John Farmer',
        phoneNumber: update.phoneNumber ?? '+91-9876543210',
        farmName: update.farmName ?? 'Green Valley Farm',
        farmLocation: update.farmLocation ?? 'Punjab, India',
        farmSizeAcres: update.farmSizeAcres ?? 10.5,
        latitude: update.latitude ?? 30.7333,
        longitude: update.longitude ?? 76.7794,
        preferredLanguage: update.preferredLanguage ?? 'en',
        userType: 'farmer',
        isActive: true,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        lastLogin: DateTime.now(),
      );

      return {
        'success': true,
        'user': updatedUser,
        'message': 'Profile updated successfully',
      };

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.put(
        '/auth/profile',
        data: update.toJson(),
      );

      if (response.statusCode == 200) {
        final user = User.fromJson(response.data);
        return {
          'success': true,
          'user': user,
          'message': 'Profile updated successfully',
        };
      } else {
        return {
          'success': false,
          'message': response.data['detail'] ?? 'Update failed',
        };
      }
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Change password
  Future<Map<String, dynamic>> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      // For demo purposes, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      return {'success': true, 'message': 'Password changed successfully'};

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.post(
        '/auth/change-password',
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'Password changed successfully',
        };
      } else {
        return {
          'success': false,
          'message': response.data['detail'] ?? 'Password change failed',
        };
      }
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Logout user
  Future<Map<String, dynamic>> logout() async {
    try {
      // For demo purposes, simulate API call
      await Future.delayed(const Duration(seconds: 1));

      return {'success': true, 'message': 'Logged out successfully'};

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.post('/auth/logout');

      return {
        'success': response.statusCode == 200,
        'message': response.statusCode == 200 
            ? 'Logged out successfully' 
            : 'Logout failed',
      };
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }

  /// Request password reset
  Future<Map<String, dynamic>> requestPasswordReset(String email) async {
    try {
      // For demo purposes, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      return {
        'success': true,
        'message': 'Password reset link sent to your email',
      };

      // TODO: Replace with actual API call
      /*
      final response = await _apiService.post(
        '/auth/forgot-password',
        data: {'email': email},
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'Password reset link sent to your email',
        };
      } else {
        return {
          'success': false,
          'message': response.data['detail'] ?? 'Failed to send reset link',
        };
      }
      */
    } catch (e) {
      return {'success': false, 'message': 'Network error: ${e.toString()}'};
    }
  }
}
