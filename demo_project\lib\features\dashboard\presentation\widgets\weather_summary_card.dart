import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../../../core/theme/app_theme.dart';

/// Weather summary card for dashboard
class WeatherSummaryCard extends StatelessWidget {
  const WeatherSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(MdiIcons.weatherSunny, color: AppTheme.primaryGreen),
                const SizedBox(width: 8),
                Text('Weather Summary', style: AppTheme.titleMedium),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to weather screen
                  },
                  child: const Text('View Details'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildWeatherItem(
                    icon: MdiIcons.thermometer,
                    label: 'Temperature',
                    value: '28°C',
                    color: AppTheme.warningOrange,
                  ),
                ),
                Expanded(
                  child: _buildWeatherItem(
                    icon: MdiIcons.waterPercent,
                    label: 'Humidity',
                    value: '65%',
                    color: AppTheme.infoBlue,
                  ),
                ),
                Expanded(
                  child: _buildWeatherItem(
                    icon: MdiIcons.weatherRainy,
                    label: 'Rainfall',
                    value: '12mm',
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.infoBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    MdiIcons.information,
                    color: AppTheme.infoBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Good conditions for fertilizer application',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.infoBlue,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeatherItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.titleMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
