"""
Weather data schemas for API validation
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class WeatherDataResponse(BaseModel):
    """Schema for weather data response"""
    id: int
    latitude: float
    longitude: float
    location_name: Optional[str]
    weather_date: datetime
    data_type: str
    
    # Temperature data
    temperature: Optional[float]
    feels_like: Optional[float]
    temp_min: Optional[float]
    temp_max: Optional[float]
    
    # Atmospheric conditions
    humidity: Optional[float]
    pressure: Optional[float]
    visibility: Optional[float]
    
    # Wind data
    wind_speed: Optional[float]
    wind_direction: Optional[float]
    wind_gust: Optional[float]
    
    # Precipitation
    rainfall: Optional[float]
    rainfall_1h: Optional[float]
    rainfall_3h: Optional[float]
    snow: Optional[float]
    
    # Cloud and weather conditions
    cloud_cover: Optional[float]
    weather_main: Optional[str]
    weather_description: Optional[str]
    weather_icon: Optional[str]
    
    # UV and solar data
    uv_index: Optional[float]
    solar_radiation: Optional[float]
    
    # Sunrise and sunset
    sunrise: Optional[datetime]
    sunset: Optional[datetime]
    
    # Additional agricultural data
    dew_point: Optional[float]
    soil_temperature: Optional[float]
    evapotranspiration: Optional[float]
    
    # Data source and quality
    data_source: str
    data_quality: str
    
    created_at: datetime
    
    class Config:
        from_attributes = True


class WeatherForecastResponse(BaseModel):
    """Schema for weather forecast response"""
    current: WeatherDataResponse
    forecast: List[WeatherDataResponse]
    location: str
    last_updated: datetime


class WeatherRequest(BaseModel):
    """Schema for weather data request"""
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    days: int = Field(5, ge=1, le=7)  # Number of forecast days
