"""
Crop schemas for API validation
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class CropResponse(BaseModel):
    """Schema for crop response"""
    id: int
    name: str
    scientific_name: Optional[str]
    common_names: Optional[str]  # JSON string
    crop_family: Optional[str]
    crop_type: Optional[str]
    growing_season: Optional[str]
    maturity_days_min: Optional[int]
    maturity_days_max: Optional[int]
    preferred_soil_type: Optional[str]  # JSON string
    ph_range_min: Optional[float]
    ph_range_max: Optional[float]
    temperature_min: Optional[float]
    temperature_max: Optional[float]
    rainfall_min: Optional[float]
    rainfall_max: Optional[float]
    nitrogen_requirement: Optional[float]
    phosphorus_requirement: Optional[float]
    potassium_requirement: Optional[float]
    description: Optional[str]
    cultivation_practices: Optional[str]  # JSON string
    common_pests: Optional[str]  # JSON string
    common_diseases: Optional[str]  # JSON string
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class CropVarietyResponse(BaseModel):
    """Schema for crop variety response"""
    id: int
    crop_id: int
    name: str
    variety_code: Optional[str]
    breeder_name: Optional[str]
    release_year: Optional[int]
    maturity_days: Optional[int]
    yield_potential: Optional[float]
    plant_height: Optional[float]
    drought_tolerance: Optional[str]
    disease_resistance: Optional[str]  # JSON string
    pest_resistance: Optional[str]  # JSON string
    grain_quality: Optional[str]
    protein_content: Optional[float]
    oil_content: Optional[float]
    suitable_regions: Optional[str]  # JSON string
    suitable_seasons: Optional[str]  # JSON string
    description: Optional[str]
    special_features: Optional[str]
    cultivation_notes: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class CropSearchRequest(BaseModel):
    """Schema for crop search request"""
    name: Optional[str] = None
    crop_family: Optional[str] = None
    growing_season: Optional[str] = None
    soil_type: Optional[str] = None
    ph_min: Optional[float] = Field(None, ge=0, le=14)
    ph_max: Optional[float] = Field(None, ge=0, le=14)
    temperature_min: Optional[float] = None
    temperature_max: Optional[float] = None
    rainfall_min: Optional[float] = None
    rainfall_max: Optional[float] = None
