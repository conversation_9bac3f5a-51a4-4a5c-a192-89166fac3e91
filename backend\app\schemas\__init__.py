"""
Pydantic schemas for API request/response validation
"""
from .user import UserCreate, UserUpdate, UserResponse, UserLogin
from .soil_health import SoilHealthCardCreate, SoilHealthCardUpdate, SoilHealthCardResponse
from .weather import WeatherDataResponse, WeatherForecastResponse
from .leaf_analysis import LeafAnalysisCreate, LeafAnalysisUpdate, LeafAnalysisResponse
from .crop import CropResponse, CropVarietyResponse
from .recommendation import RecommendationCreate, RecommendationResponse, RecommendationUpdate

__all__ = [
    "UserCreate", "UserUpdate", "UserResponse", "UserLogin",
    "SoilHealthCardCreate", "SoilHealthCardUpdate", "SoilHealthCardResponse",
    "WeatherDataResponse", "WeatherForecastResponse",
    "LeafAnalysisCreate", "LeafAnalysisUpdate", "LeafAnalysisResponse",
    "CropResponse", "CropVarietyResponse",
    "RecommendationCreate", "RecommendationResponse", "RecommendationUpdate"
]
