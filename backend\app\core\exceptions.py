"""
Custom exceptions for the application
"""
from fastapi import HTTPException, status


class FarmSmartException(Exception):
    """Base exception for FarmSmart application"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class ValidationError(FarmSmartException):
    """Raised when data validation fails"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class NotFoundError(FarmSmartException):
    """Raised when a resource is not found"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_404_NOT_FOUND)


class UnauthorizedError(FarmSmartException):
    """Raised when user is not authorized"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_401_UNAUTHORIZED)


class ForbiddenError(FarmSmartException):
    """Raised when user doesn't have permission"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_403_FORBIDDEN)


class ServiceUnavailableError(FarmSmartException):
    """Raised when external service is unavailable"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_503_SERVICE_UNAVAILABLE)


class DatabaseError(FarmSmartException):
    """Raised when database operation fails"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)


class FileUploadError(FarmSmartException):
    """Raised when file upload fails"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class WeatherServiceError(ServiceUnavailableError):
    """Raised when weather service is unavailable"""
    def __init__(self, message: str = "Weather service is currently unavailable"):
        super().__init__(message)


class ImageProcessingError(FarmSmartException):
    """Raised when image processing fails"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_422_UNPROCESSABLE_ENTITY)


class MLModelError(FarmSmartException):
    """Raised when ML model operation fails"""
    def __init__(self, message: str):
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)
